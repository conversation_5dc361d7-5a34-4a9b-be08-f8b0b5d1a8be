#!/usr/bin/env python3
"""
测试运行器 - 简化版
统一的测试入口点
"""

import argparse
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_final_tests():
    """运行最终测试"""
    from test_final import main as final_main
    print("🎯 运行最终统一测试...")
    final_main()

def run_framework_tests():
    """运行框架测试"""
    from test_framework import main as framework_main
    print("🧪 运行框架测试...")
    framework_main()

def main():
    parser = argparse.ArgumentParser(description='Polars算子测试运行器')
    parser.add_argument('--test', choices=['final', 'framework', 'all'],
                       default='final', help='选择要运行的测试类型')

    args = parser.parse_args()

    if args.test == 'final':
        run_final_tests()
    elif args.test == 'framework':
        run_framework_tests()
    elif args.test == 'all':
        run_final_tests()
        print("\n" + "="*50 + "\n")
        run_framework_tests()

if __name__ == "__main__":
    main()
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Polars算子测试运行器")
    parser.add_argument("--test", "-t", 
                       choices=["accuracy", "performance", "comprehensive", "all"],
                       default="all",
                       help="选择测试类型")
    
    args = parser.parse_args()
    
    if args.test == "accuracy":
        run_accuracy_tests()
    elif args.test == "performance":
        run_performance_tests()
    elif args.test == "comprehensive":
        run_comprehensive_tests()
    elif args.test == "all":
        run_all_tests()

if __name__ == "__main__":
    main()
