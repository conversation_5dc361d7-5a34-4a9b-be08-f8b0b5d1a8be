#!/usr/bin/env python3
"""
专项性能测试
对比不同实现的性能表现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_framework import PerformanceTester, create_test_data
from operators.pandas_reference import PandasOperators
from operators.polars_native import PolarsNativeOperators
from operators.polars_numba import PolarsNumbaOperators

def test_performance_scaling():
    """测试性能扩展性"""
    print("性能扩展性测试")
    print("=" * 50)
    
    sizes = [1000, 5000, 10000]
    window = 20
    
    for size in sizes:
        print(f"\n数据规模: {size} 行")
        print("-" * 30)
        
        # 创建测试数据
        pl_df, pd_df = create_test_data(n_rows=size, n_symbols=10)
        tester = PerformanceTester(pl_df, pd_df)
        
        # 测试rolling_rank
        tester.benchmark_implementations(
            "rolling_rank",
            {
                "numba": PolarsNumbaOperators.rolling_rank,
                "native": PolarsNativeOperators.rolling_rank,
            },
            PandasOperators.rolling_rank,
            ("Close", window),
            rounds=1  # 大数据集只测试一轮
        )

def test_all_operators_performance():
    """测试所有算子的性能"""
    print("\n所有算子性能对比")
    print("=" * 50)
    
    # 创建中等规模测试数据
    pl_df, pd_df = create_test_data(n_rows=5000, n_symbols=10)
    tester = PerformanceTester(pl_df, pd_df)
    
    window = 20
    
    # 单列算子性能测试
    single_col_tests = [
        ("rolling_rank", {"numba": PolarsNumbaOperators.rolling_rank, "native": PolarsNativeOperators.rolling_rank}, PandasOperators.rolling_rank, ("Close", window)),
    ]
    
    for name, implementations, pandas_func, args in single_col_tests:
        tester.benchmark_implementations(name, implementations, pandas_func, args)
    
    # 双列算子性能测试
    double_col_tests = [
        ("rolling_cov", {"numba": PolarsNumbaOperators.rolling_cov, "manual": PolarsNativeOperators.rolling_cov_manual}, PandasOperators.rolling_cov, ("Volume", "Close", window)),
        ("rolling_corr", {"numba": PolarsNumbaOperators.rolling_corr, "manual": PolarsNativeOperators.rolling_corr_manual}, PandasOperators.rolling_corr, ("Volume", "Close", window)),
    ]
    
    for name, implementations, pandas_func, args in double_col_tests:
        tester.benchmark_implementations(name, implementations, pandas_func, args)
    
    # 打印性能总结
    print("\n" + "=" * 50)
    print("性能测试总结")
    print("=" * 50)
    
    print(f"{'算子':<15} {'实现':<10} {'加速比':<10}")
    print("-" * 40)
    
    for result in tester.results:
        name = result["name"]
        for impl_name, impl_result in result["implementations"].items():
            speedup = impl_result["speedup"]
            print(f"{name:<15} {impl_name:<10} {speedup:<10.2f}x")

def main():
    """主测试函数"""
    test_performance_scaling()
    test_all_operators_performance()

if __name__ == "__main__":
    main()
