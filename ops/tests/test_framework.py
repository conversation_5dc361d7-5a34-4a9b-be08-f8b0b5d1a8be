#!/usr/bin/env python3
"""
统一测试框架
支持准确性测试、性能测试、多实现对比
"""

import polars as pl
import pandas as pd
import numpy as np
import time
from typing import Dict, List, Tuple, Any, Callable
import warnings
warnings.filterwarnings('ignore')

# 导入算子实现
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operators.pandas_reference import PandasOperators
from operators.polars_native import PolarsNativeOperators
from operators.polars_numba import PolarsNumbaOperators
from operators.config import CONFIG, load_data

# ============================================================================
# 测试数据生成
# ============================================================================

def create_test_data(n_rows: int = 1000, n_symbols: int = 5, seed: int = 42) -> Tuple[pl.DataFrame, pd.DataFrame]:
    """创建测试数据 - 使用更稳定的数据模式"""
    np.random.seed(seed)

    data = []
    for symbol_id in range(n_symbols):
        symbol = f"SYM{symbol_id:03d}"
        base_price = 100 + symbol_id * 10  # 每个symbol有不同的基础价格
        base_volume = 1000 + symbol_id * 100

        for i in range(n_rows // n_symbols):
            # 使用更稳定的趋势数据而不是纯随机数据
            trend = i * 0.1  # 轻微上升趋势
            noise = np.random.randn() * 0.5  # 减少噪声

            price = base_price + trend + noise
            volume = base_volume + i * 2 + int(np.random.randn() * 50)

            data.append({
                "symbol": symbol,
                "datetime": pd.Timestamp("2023-01-01") + pd.Timedelta(minutes=i),
                "Close": price,
                "Volume": max(100, volume),  # 确保volume为正
                "Open": price + np.random.randn() * 0.2,
                "High": price + abs(np.random.randn()) * 0.3,
                "Low": price - abs(np.random.randn()) * 0.3,
                "Vwap": price + np.random.randn() * 0.1,
            })

    pl_df = pl.DataFrame(data).sort(["symbol", "datetime"])
    pd_df = pd.DataFrame(data).sort_values(["symbol", "datetime"]).reset_index(drop=True)

    return pl_df, pd_df

def _data(max_symbols: int = 10, max_rows: int = 50000, use_full_data: bool = False) -> Tuple[pl.DataFrame, pd.DataFrame]:
    """加载真实数据"""
    try:
        pl_df = load_data().sort(["symbol", "datetime"])
        print(f"原始数据大小: {pl_df.shape}")

        if use_full_data:
            print("使用全量数据进行测试")
            # 使用全量数据，但可以限制symbol数量以控制内存
            if max_symbols > 0:
                sample_symbols = pl_df["symbol"].unique().head(max_symbols).to_list()
                pl_df = pl_df.filter(pl.col("symbol").is_in(sample_symbols))
                print(f"限制symbol数量后: {pl_df.shape}")
        else:
            # 取样本数据
            sample_symbols = pl_df["symbol"].unique().head(max_symbols).to_list()
            pl_df = pl_df.filter(pl.col("symbol").is_in(sample_symbols)).head(max_rows)
            print(f"采样后数据大小: {pl_df.shape}")

        pd_df = pl_df.to_pandas()
        return pl_df, pd_df
    except Exception as e:
        print(f"无法加载真实数据: {e}")
        return create_test_data(max_rows, max_symbols)

# ============================================================================
# 准确性测试器
# ============================================================================

class AccuracyTester:
    """准确性测试器"""
    
    def __init__(self, pl_df: pl.DataFrame, pd_df: pd.DataFrame):
        self.pl_df = pl_df
        self.pd_df = pd_df
        self.results = []
    
    def test_single_column_operator(self,
                                  name: str,
                                  pandas_func: Callable,
                                  polars_func: Callable,
                                  col: str,
                                  window: int,
                                  tolerance: float = 1e-10) -> Dict[str, Any]:
        """测试单列算子"""
        print(f"\n测试 {name} (列: {col}, 窗口: {window})")
        print("-" * 50)

        # 确保数据按相同顺序排序
        pd_df_sorted = self.pd_df.sort_values(["symbol", "datetime"]).reset_index(drop=True)
        pl_df_sorted = self.pl_df.sort(["symbol", "datetime"])

        # Pandas参考结果
        try:
            pd_result = pandas_func(pd_df_sorted, col, window, by_group="symbol").to_numpy()
            pandas_success = True
        except Exception as e:
            print(f"✗ Pandas失败: {e}")
            return {"name": name, "success": False, "error": str(e)}

        # Polars结果（按symbol分组，保持相同顺序）
        try:
            pl_result_list = []
            # 按pandas相同的symbol顺序处理
            for symbol in pd_df_sorted["symbol"].unique():
                symbol_df = pl_df_sorted.filter(pl.col("symbol") == symbol)
                result = symbol_df.with_columns([
                    polars_func(col, window).alias("result")
                ])["result"].to_numpy()
                pl_result_list.append(result)

            pl_result = np.concatenate(pl_result_list)
            polars_success = True
        except Exception as e:
            print(f"✗ Polars失败: {e}")
            return {"name": name, "success": False, "error": str(e)}

        # 准确性分析
        return self._analyze_accuracy(name, pd_result, pl_result, tolerance)
    
    def test_double_column_operator(self,
                                  name: str,
                                  pandas_func: Callable,
                                  polars_func: Callable,
                                  col_x: str,
                                  col_y: str,
                                  window: int,
                                  tolerance: float = 1e-10) -> Dict[str, Any]:
        """测试双列算子"""
        print(f"\n测试 {name} (列: {col_x}, {col_y}, 窗口: {window})")
        print("-" * 50)

        # 确保数据按相同顺序排序
        pd_df_sorted = self.pd_df.sort_values(["symbol", "datetime"]).reset_index(drop=True)
        pl_df_sorted = self.pl_df.sort(["symbol", "datetime"])

        # Pandas参考结果
        try:
            pd_result = pandas_func(pd_df_sorted, col_x, col_y, window, by_group="symbol").to_numpy()
            pandas_success = True
        except Exception as e:
            print(f"✗ Pandas失败: {e}")
            return {"name": name, "success": False, "error": str(e)}

        # Polars结果（按symbol分组，保持相同顺序）
        try:
            pl_result_list = []
            # 按pandas相同的symbol顺序处理
            for symbol in pd_df_sorted["symbol"].unique():
                symbol_df = pl_df_sorted.filter(pl.col("symbol") == symbol)
                result = symbol_df.with_columns([
                    polars_func(col_x, col_y, window).alias("result")
                ])["result"].to_numpy()
                pl_result_list.append(result)

            pl_result = np.concatenate(pl_result_list)
            polars_success = True
        except Exception as e:
            print(f"✗ Polars失败: {e}")
            return {"name": name, "success": False, "error": str(e)}

        # 准确性分析
        return self._analyze_accuracy(name, pd_result, pl_result, tolerance)
    
    def _analyze_accuracy(self, name: str, pd_result: np.ndarray, pl_result: np.ndarray, tolerance: float) -> Dict[str, Any]:
        """分析准确性"""
        # 基本统计
        total_count = len(pd_result)
        pd_nan_count = np.isnan(pd_result).sum()
        pl_nan_count = np.isnan(pl_result).sum()
        
        # 有效值比较
        valid_mask = ~(np.isnan(pd_result) | np.isnan(pl_result))
        valid_count = valid_mask.sum()
        
        if valid_count == 0:
            print("没有有效值可比较")
            return {"name": name, "success": False, "error": "No valid values"}
        
        pd_valid = pd_result[valid_mask]
        pl_valid = pl_result[valid_mask]
        
        # 计算误差
        errors = np.abs(pd_valid - pl_valid)
        max_error = np.max(errors)
        mean_error = np.mean(errors)
        median_error = np.median(errors)
        
        # 超过容忍度的数量
        exceed_count = (errors > tolerance).sum()
        exceed_ratio = exceed_count / len(errors)
        
        accuracy_ok = max_error < tolerance
        
        result = {
            "name": name,
            "success": True,
            "total_count": total_count,
            "valid_count": valid_count,
            "pd_nan_count": pd_nan_count,
            "pl_nan_count": pl_nan_count,
            "max_error": max_error,
            "mean_error": mean_error,
            "median_error": median_error,
            "exceed_count": exceed_count,
            "exceed_ratio": exceed_ratio,
            "accuracy_ok": accuracy_ok,
            "tolerance": tolerance
        }
        
        # 打印结果
        print(f"总数据量: {total_count}")
        print(f"有效值数量: {valid_count} ({valid_count/total_count*100:.1f}%)")
        print(f"最大误差: {max_error:.6e}")
        print(f"平均误差: {mean_error:.6e}")
        print(f"超过容忍度的数量: {exceed_count} ({exceed_ratio*100:.2f}%)")
        print(f"准确性: {'✓' if accuracy_ok else '✗'}")
        
        self.results.append(result)
        return result

# ============================================================================
# 性能测试器
# ============================================================================

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, pl_df: pl.DataFrame, pd_df: pd.DataFrame):
        self.pl_df = pl_df
        self.pd_df = pd_df
        self.results = []
    
    def benchmark_implementations(self,
                                name: str,
                                implementations: Dict[str, Callable],
                                pandas_func: Callable,
                                args: Tuple,
                                rounds: int = 3) -> Dict[str, Any]:
        """性能基准测试"""
        print(f"\n性能测试: {name}")
        print("=" * 50)
        
        # Pandas基准
        pandas_times = []
        for _ in range(rounds):
            start_time = time.time()
            try:
                if len(args) == 2:  # 单列算子
                    pandas_func(self.pd_df, args[0], args[1], by_group="symbol")
                else:  # 双列算子
                    pandas_func(self.pd_df, args[0], args[1], args[2], by_group="symbol")
                pandas_times.append(time.time() - start_time)
            except Exception as e:
                print(f"Pandas失败: {e}")
                pandas_times.append(float('inf'))
        
        pandas_time = np.mean(pandas_times)
        print(f"Pandas基准: {pandas_time:.4f}s")
        
        # 测试各种实现
        impl_results = {}
        
        for impl_name, impl_func in implementations.items():
            impl_times = []
            
            for _ in range(rounds):
                start_time = time.time()
                try:
                    # 按symbol分组处理
                    for symbol in self.pl_df["symbol"].unique():
                        symbol_df = self.pl_df.filter(pl.col("symbol") == symbol)
                        
                        if len(args) == 2:  # 单列算子
                            symbol_df.with_columns([
                                impl_func(args[0], args[1]).alias("result")
                            ])
                        else:  # 双列算子
                            symbol_df.with_columns([
                                impl_func(args[0], args[1], args[2]).alias("result")
                            ])
                    
                    impl_times.append(time.time() - start_time)
                except Exception as e:
                    print(f"{impl_name}失败: {e}")
                    impl_times.append(float('inf'))
            
            impl_time = np.mean(impl_times)
            speedup = pandas_time / impl_time if impl_time > 0 else 0
            
            impl_results[impl_name] = {
                "time": impl_time,
                "speedup": speedup
            }
            
            print(f"{impl_name}: {impl_time:.4f}s (加速比: {speedup:.2f}x)")
        
        result = {
            "name": name,
            "pandas_time": pandas_time,
            "implementations": impl_results
        }
        
        self.results.append(result)
        return result

# ============================================================================
# 综合测试器
# ============================================================================

class ComprehensiveTester:
    """综合测试器"""
    
    def __init__(self, pl_df: pl.DataFrame, pd_df: pd.DataFrame):
        self.accuracy_tester = AccuracyTester(pl_df, pd_df)
        self.performance_tester = PerformanceTester(pl_df, pd_df)
    
    def run_all_tests(self, window: int = 20):
        """运行所有测试"""
        print("开始综合测试")
        print("=" * 60)
        
        # 准确性测试
        print("\n📊 准确性测试")
        print("=" * 30)
        
        # 单列算子测试
        self.accuracy_tester.test_single_column_operator(
            "rolling_rank", 
            PandasOperators.rolling_rank,
            PolarsNumbaOperators.rolling_rank,
            "Close", window
        )
        
        # 双列算子测试
        self.accuracy_tester.test_double_column_operator(
            "rolling_cov",
            PandasOperators.rolling_cov,
            PolarsNumbaOperators.rolling_cov,
            "Volume", "Close", window
        )
        
        self.accuracy_tester.test_double_column_operator(
            "rolling_corr",
            PandasOperators.rolling_corr,
            PolarsNumbaOperators.rolling_corr,
            "Volume", "Close", window
        )
        
        # 性能测试
        print("\n⚡ 性能测试")
        print("=" * 30)
        
        # rolling_rank性能对比
        self.performance_tester.benchmark_implementations(
            "rolling_rank",
            {
                "numba": PolarsNumbaOperators.rolling_rank,
                "native": PolarsNativeOperators.rolling_rank,
            },
            PandasOperators.rolling_rank,
            ("Close", window)
        )
        
        # rolling_cov性能对比
        self.performance_tester.benchmark_implementations(
            "rolling_cov",
            {
                "numba": PolarsNumbaOperators.rolling_cov,
                "manual": PolarsNativeOperators.rolling_cov_manual,
            },
            PandasOperators.rolling_cov,
            ("Volume", "Close", window)
        )
    
    def print_summary(self):
        """打印测试总结"""
        print("\n📋 测试总结")
        print("=" * 60)
        
        # 准确性总结
        print("\n准确性测试结果:")
        print("-" * 40)
        for result in self.accuracy_tester.results:
            if result.get("success", False):
                status = "✓" if result["accuracy_ok"] else "✗"
                print(f"{result['name']:<15} {status} (最大误差: {result['max_error']:.2e})")
            else:
                print(f"{result['name']:<15} ✗ (失败)")
        
        # 性能总结
        print("\n性能测试结果:")
        print("-" * 40)
        for result in self.performance_tester.results:
            print(f"\n{result['name']}:")
            for impl_name, impl_result in result["implementations"].items():
                print(f"  {impl_name}: {impl_result['speedup']:.2f}x")

def main():
    """主测试函数"""
    # 创建测试数据
    print("创建测试数据...")
    pl_df, pd_df = create_test_data(n_rows=5000, n_symbols=10)
    print(f"数据大小: {pl_df.shape}")
    
    # 运行综合测试
    tester = ComprehensiveTester(pl_df, pd_df)
    tester.run_all_tests(window=20)
    tester.print_summary()

if __name__ == "__main__":
    main()
