#!/usr/bin/env python3
"""
最终统一测试脚本
专注于真实数据的准确性测试，使用1e-4误差阈值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import polars as pl
import pandas as pd
import numpy as np
import time
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

from operators.pandas_reference import PandasOperators
from operators.polars_operators import PolarsOperators
from operators.config import load_data

class FinalTester:
    """最终测试器 - 专注准确性和性能"""
    
    def __init__(self, max_rows: int = 100000):
        self.max_rows = max_rows
        self.error_threshold = 1e-4  # 误差阈值
        self.load_data()
    
    def load_data(self):
        """加载真实数据"""
        print("正在加载真实数据...")
        try:
            full_df = load_data().sort(["symbol", "datetime"])
            print(f"原始数据大小: {full_df.shape}")
            
            self.pl_df = full_df.head(self.max_rows)
            print(f"测试数据大小: {self.pl_df.shape}")
            print(f"包含symbols: {len(self.pl_df['symbol'].unique())}")
            print(f"时间范围: {self.pl_df['datetime'].min()} 到 {self.pl_df['datetime'].max()}")
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            raise
    
    def analyze_accuracy(self, name: str, pd_result: np.ndarray, pl_result: np.ndarray) -> Dict[str, Any]:
        """详细的准确性分析"""
        # 基本统计
        total_count = len(pd_result)
        pd_nan_count = np.isnan(pd_result).sum()
        pl_nan_count = np.isnan(pl_result).sum()
        
        # 有效值比较
        valid_mask = ~(np.isnan(pd_result) | np.isnan(pl_result))
        valid_count = valid_mask.sum()
        
        if valid_count == 0:
            return {
                "name": name,
                "success": False,
                "error": "No valid values to compare"
            }
        
        pd_valid = pd_result[valid_mask]
        pl_valid = pl_result[valid_mask]
        
        # 计算误差
        errors = np.abs(pd_valid - pl_valid)
        max_error = np.max(errors)
        mean_error = np.mean(errors)
        median_error = np.median(errors)
        
        # 超过阈值的统计
        exceed_threshold_count = (errors > self.error_threshold).sum()
        exceed_threshold_ratio = exceed_threshold_count / len(errors)
        
        # 不同误差级别的统计
        error_levels = [1e-10, 1e-8, 1e-6, 1e-4, 1e-2]
        error_distribution = {}
        for level in error_levels:
            count = (errors > level).sum()
            ratio = count / len(errors)
            error_distribution[f">{level:.0e}"] = {"count": count, "ratio": ratio}
        
        accuracy_ok = exceed_threshold_count == 0
        
        return {
            "name": name,
            "success": True,
            "total_count": total_count,
            "valid_count": valid_count,
            "pd_nan_count": pd_nan_count,
            "pl_nan_count": pl_nan_count,
            "max_error": max_error,
            "mean_error": mean_error,
            "median_error": median_error,
            "exceed_threshold_count": exceed_threshold_count,
            "exceed_threshold_ratio": exceed_threshold_ratio,
            "error_distribution": error_distribution,
            "accuracy_ok": accuracy_ok,
            "threshold": self.error_threshold
        }
    
    def test_accuracy(self, window: int = 20, test_rows: int = 50000):
        """准确性测试"""
        print(f"\n{'='*80}")
        print(f"准确性测试 (测试行数: {test_rows}, 窗口: {window}, 阈值: {self.error_threshold})")
        print(f"{'='*80}")
        
        test_df = self.pl_df.head(test_rows)
        test_pd_df = test_df.to_pandas()
        
        # 测试算子列表
        test_cases = [
            ("rolling_rank", "Close", PandasOperators.rolling_rank, PolarsOperators.rolling_rank),
            ("rolling_cov", ("Volume", "Close"), PandasOperators.rolling_cov, PolarsOperators.rolling_cov),
            ("rolling_corr", ("Volume", "Close"), PandasOperators.rolling_corr, PolarsOperators.rolling_corr),
            ("rolling_regbeta", ("Volume", "Close"), PandasOperators.rolling_regbeta, PolarsOperators.rolling_regbeta),
            ("rolling_regres", ("Volume", "Close"), PandasOperators.rolling_regres, PolarsOperators.rolling_regres),
            ("decaylinear", "Close", PandasOperators.decaylinear, PolarsOperators.decaylinear),
        ]
        
        results = {}
        
        for name, cols, pandas_func, polars_func in test_cases:
            print(f"\n测试 {name}...")
            
            try:
                # Pandas参考结果
                start_time = time.time()
                if isinstance(cols, tuple):
                    pd_result = pandas_func(test_pd_df, cols[0], cols[1], window, by_group="symbol").to_numpy()
                else:
                    pd_result = pandas_func(test_pd_df, cols, window, by_group="symbol").to_numpy()
                pandas_time = time.time() - start_time

                # Polars结果 - 使用新的算子API（无by_group参数，需要手动添加.over()）
                start_time = time.time()
                if isinstance(cols, tuple):
                    pl_result = test_df.with_columns([
                        polars_func(cols[0], cols[1], window).over("symbol").alias("result")
                    ])["result"].to_numpy()
                else:
                    pl_result = test_df.with_columns([
                        polars_func(cols, window).over("symbol").alias("result")
                    ])["result"].to_numpy()
                polars_time = time.time() - start_time
                
                # 准确性分析
                analysis = self.analyze_accuracy(name, pd_result, pl_result)
                analysis["pandas_time"] = pandas_time
                analysis["polars_time"] = polars_time
                analysis["speedup"] = pandas_time / polars_time if polars_time > 0 else np.nan
                results[name] = analysis
                
                if analysis["success"]:
                    print(f"  📊 有效值: {analysis['valid_count']}/{analysis['total_count']}")
                    print(f"  📈 最大误差: {analysis['max_error']:.2e}")
                    print(f"  📉 平均误差: {analysis['mean_error']:.2e}")
                    print(f"  🚨 超过阈值({self.error_threshold})的行数: {analysis['exceed_threshold_count']} ({analysis['exceed_threshold_ratio']*100:.2f}%)")
                    print(f"  ⚡ 加速比: {analysis['speedup']:.2f}x")
                    print(f"  ✓ 准确性: {'通过' if analysis['accuracy_ok'] else '失败'}")
                else:
                    print(f"  ✗ 测试失败: {analysis.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"  ✗ 测试失败: {e}")
                results[name] = {"name": name, "success": False, "error": str(e)}
        
        return results
    
    def test_performance(self, window: int = 20):
        """性能测试"""
        print(f"\n{'='*80}")
        print(f"性能测试 (数据大小: {self.pl_df.shape}, 窗口: {window})")
        print(f"{'='*80}")
        
        # 测试所有算子的性能
        algorithms = [
            ("rolling_rank", "Close", PolarsOperators.rolling_rank),
            ("rolling_cov", ("Volume", "Close"), PolarsOperators.rolling_cov),
            ("rolling_corr", ("Volume", "Close"), PolarsOperators.rolling_corr),
            ("rolling_regbeta", ("Volume", "Close"), PolarsOperators.rolling_regbeta),
            ("rolling_regres", ("Volume", "Close"), PolarsOperators.rolling_regres),
            ("decaylinear", "Close", PolarsOperators.decaylinear),
        ]
        
        results = {}
        
        for name, cols, func in algorithms:
            print(f"\n测试 {name}:")
            
            try:
                start_time = time.time()
                if isinstance(cols, tuple):
                    result = self.pl_df.with_columns([
                        func(cols[0], cols[1], window).over("symbol").alias("result")
                    ])
                else:
                    result = self.pl_df.with_columns([
                        func(cols, window).over("symbol").alias("result")
                    ])
                total_time = time.time() - start_time
                
                # 统计结果
                result_col = result["result"]
                valid_count = result_col.drop_nulls().len()
                null_count = result_col.null_count()
                throughput = len(self.pl_df) / total_time
                
                results[name] = {
                    "total_time": total_time,
                    "throughput": throughput,
                    "valid_count": valid_count,
                    "null_count": null_count,
                    "valid_ratio": valid_count / (valid_count + null_count) if (valid_count + null_count) > 0 else 0
                }
                
                print(f"  ⏱️  总时间: {total_time:.3f}s")
                print(f"  🚀 吞吐量: {throughput:.0f} 行/秒")
                print(f"  📊 有效结果: {valid_count}/{len(self.pl_df)} ({results[name]['valid_ratio']*100:.1f}%)")
                
            except Exception as e:
                print(f"  ✗ 测试失败: {e}")
                results[name] = {"error": str(e)}
        
        return results
    
    def print_summary(self, accuracy_results: Dict, performance_results: Dict):
        """打印测试总结"""
        print(f"\n{'='*100}")
        print("最终测试总结")
        print(f"{'='*100}")
        
        print(f"\n📊 数据规模: {len(self.pl_df):,} 行, {len(self.pl_df['symbol'].unique())} symbols")
        
        print(f"\n🎯 准确性测试结果 (阈值: {self.error_threshold}):")
        print(f"{'算子':<15} {'状态':<8} {'最大误差':<12} {'超阈值行数':<12} {'超阈值比例':<12} {'加速比':<10}")
        print("-" * 85)
        
        for name, result in accuracy_results.items():
            if result.get("success", False):
                status = "✓" if result["accuracy_ok"] else "✗"
                max_error = f"{result['max_error']:.2e}"
                exceed_count = result['exceed_threshold_count']
                exceed_ratio = f"{result['exceed_threshold_ratio']*100:.2f}%"
                speedup = f"{result.get('speedup', 0):.2f}x" if result.get('speedup') else "N/A"
                print(f"{name:<15} {status:<8} {max_error:<12} {exceed_count:<12} {exceed_ratio:<12} {speedup:<10}")
            else:
                print(f"{name:<15} {'✗':<8} {'ERROR':<12} {'N/A':<12} {'N/A':<12} {'N/A':<10}")
        
        print(f"\n⚡ 性能测试结果:")
        print(f"{'算子':<15} {'时间(s)':<10} {'吞吐量(行/s)':<15} {'有效率':<10}")
        print("-" * 55)
        
        for name, result in performance_results.items():
            if "error" not in result:
                total_time = f"{result['total_time']:.3f}"
                throughput = f"{result['throughput']:.0f}"
                valid_rate = f"{result['valid_ratio']*100:.1f}%"
                print(f"{name:<15} {total_time:<10} {throughput:<15} {valid_rate:<10}")
            else:
                print(f"{name:<15} {'ERROR':<10} {'N/A':<15} {'N/A':<10}")
        
        # 统计通过率
        total_tests = len(accuracy_results)
        passed_tests = sum(1 for r in accuracy_results.values() if r.get("success", False) and r.get("accuracy_ok", False))
        pass_rate = passed_tests / total_tests * 100 if total_tests > 0 else 0
        
        print(f"\n📈 总体结果:")
        print(f"  准确性通过率: {passed_tests}/{total_tests} ({pass_rate:.1f}%)")
        print(f"  所有算子均使用Numba加速，确保最佳性能")
        print(f"  建议在生产环境中使用这些经过验证的算子")

def main():
    """主测试函数"""
    print("Polars算子最终测试")
    print("=" * 80)
    
    # 创建测试器
    tester = FinalTester(max_rows=10000000)
    
    # 运行测试
    accuracy_results = tester.test_accuracy(window=20, test_rows=50000)
    performance_results = tester.test_performance(window=20)
    
    # 打印总结
    tester.print_summary(accuracy_results, performance_results)

if __name__ == "__main__":
    main()
