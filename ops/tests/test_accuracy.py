#!/usr/bin/env python3
"""
专项准确性测试
重点测试算子的计算准确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_framework import AccuracyTester, create_test_data
from operators.pandas_reference import PandasOperators
from operators.polars_numba import PolarsNumbaOperators

def test_all_operators():
    """测试所有算子的准确性"""
    print("专项准确性测试")
    print("=" * 50)
    
    # 创建测试数据
    pl_df, pd_df = create_test_data(n_rows=1000, n_symbols=5)
    tester = AccuracyTester(pl_df, pd_df)
    
    window = 10
    tolerance = 1e-10
    
    # 测试所有单列算子
    single_col_tests = [
        ("rolling_rank", PandasOperators.rolling_rank, PolarsNumbaOperators.rolling_rank, "Close"),
        ("decaylinear", PandasOperators.decaylinear, PolarsNumbaOperators.decaylinear, "Close"),
        ("rolling_mean", PandasOperators.rolling_mean, PolarsNumbaOperators.rolling_mean, "Close"),
        ("rolling_std", PandasOperators.rolling_std, PolarsNumbaOperators.rolling_std, "Close"),
    ]
    
    for name, pandas_func, polars_func, col in single_col_tests:
        tester.test_single_column_operator(name, pandas_func, polars_func, col, window, tolerance)
    
    # 测试所有双列算子
    double_col_tests = [
        ("rolling_cov", PandasOperators.rolling_cov, PolarsNumbaOperators.rolling_cov, "Volume", "Close"),
        ("rolling_corr", PandasOperators.rolling_corr, PolarsNumbaOperators.rolling_corr, "Volume", "Close"),
        ("rolling_regbeta", PandasOperators.rolling_regbeta, PolarsNumbaOperators.rolling_regbeta, "Volume", "Close"),
        ("rolling_regres", PandasOperators.rolling_regres, PolarsNumbaOperators.rolling_regres, "Volume", "Close"),
    ]
    
    for name, pandas_func, polars_func, col_x, col_y in double_col_tests:
        tester.test_double_column_operator(name, pandas_func, polars_func, col_x, col_y, window, tolerance)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("准确性测试总结")
    print("=" * 50)
    
    passed = 0
    total = len(tester.results)
    
    for result in tester.results:
        if result.get("success", False) and result.get("accuracy_ok", False):
            status = "✓ PASS"
            passed += 1
        else:
            status = "✗ FAIL"
        
        max_error = result.get("max_error", float('inf'))
        print(f"{result['name']:<20} {status} (误差: {max_error:.2e})")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")

if __name__ == "__main__":
    test_all_operators()
