{"cells": [{"cell_type": "code", "execution_count": 22, "id": "bf4e896c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (44_220_825, 11)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>symbol</th><th>hhmm</th><th>Open</th><th>High</th><th>Low</th><th>Close</th><th>Volume</th><th>Amount</th><th>Vwap</th><th>datetime</th></tr><tr><td>str</td><td>str</td><td>str</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>datetime[μs]</td></tr></thead><tbody><tr><td>&quot;20230601&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0925&quot;</td><td>11.6</td><td>11.6</td><td>11.6</td><td>11.6</td><td>417700.0</td><td>4.84532e6</td><td>11.6</td><td>2023-06-01 09:25:00</td></tr><tr><td>&quot;20230601&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0930&quot;</td><td>11.6</td><td>11.6</td><td>11.55</td><td>11.56</td><td>1.510606e6</td><td>1.7477944e7</td><td>11.570154</td><td>2023-06-01 09:30:00</td></tr><tr><td>&quot;20230601&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0931&quot;</td><td>11.56</td><td>11.58</td><td>11.54</td><td>11.57</td><td>1.072172e6</td><td>1.2397453e7</td><td>11.562933</td><td>2023-06-01 09:31:00</td></tr><tr><td>&quot;20230601&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0932&quot;</td><td>11.57</td><td>11.6</td><td>11.56</td><td>11.6</td><td>1.4002e6</td><td>1.6215498e7</td><td>11.580844</td><td>2023-06-01 09:32:00</td></tr><tr><td>&quot;20230601&quot;</td><td>&quot;000001.SZ&quot;</td><td>&quot;0933&quot;</td><td>11.6</td><td>11.63</td><td>11.58</td><td>11.62</td><td>518216.0</td><td>6.011253e6</td><td>11.599898</td><td>2023-06-01 09:33:00</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20230731&quot;</td><td>&quot;688332.SH&quot;</td><td>&quot;1456&quot;</td><td>69.199997</td><td>69.209999</td><td>69.199997</td><td>69.204491</td><td>1303.0</td><td>90172.09375</td><td>69.203445</td><td>2023-07-31 14:56:00</td></tr><tr><td>&quot;20230731&quot;</td><td>&quot;688332.SH&quot;</td><td>&quot;1457&quot;</td><td>69.204491</td><td>69.204491</td><td>69.204491</td><td>69.204491</td><td>0.0</td><td>0.0</td><td>69.203445</td><td>2023-07-31 14:57:00</td></tr><tr><td>&quot;20230731&quot;</td><td>&quot;688332.SH&quot;</td><td>&quot;1458&quot;</td><td>69.204491</td><td>69.204491</td><td>69.204491</td><td>69.204491</td><td>0.0</td><td>0.0</td><td>69.203445</td><td>2023-07-31 14:58:00</td></tr><tr><td>&quot;20230731&quot;</td><td>&quot;688332.SH&quot;</td><td>&quot;1459&quot;</td><td>69.204491</td><td>69.204491</td><td>69.204491</td><td>69.204491</td><td>0.0</td><td>0.0</td><td>69.203445</td><td>2023-07-31 14:59:00</td></tr><tr><td>&quot;20230731&quot;</td><td>&quot;688332.SH&quot;</td><td>&quot;1500&quot;</td><td>69.230003</td><td>69.230003</td><td>69.230003</td><td>69.230003</td><td>8319.0</td><td>575924.375</td><td>69.230003</td><td>2023-07-31 15:00:00</td></tr></tbody></table></div>"], "text/plain": ["shape: (44_220_825, 11)\n", "┌──────────┬───────────┬──────┬───────────┬───┬────────────┬─────────────┬───────────┬─────────────┐\n", "│ date     ┆ symbol    ┆ hhmm ┆ Open      ┆ … ┆ Volume     ┆ Amount      ┆ Vwap      ┆ datetime    │\n", "│ ---      ┆ ---       ┆ ---  ┆ ---       ┆   ┆ ---        ┆ ---         ┆ ---       ┆ ---         │\n", "│ str      ┆ str       ┆ str  ┆ f32       ┆   ┆ f32        ┆ f32         ┆ f32       ┆ datetime[μs │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ ]           │\n", "╞══════════╪═══════════╪══════╪═══════════╪═══╪════════════╪═════════════╪═══════════╪═════════════╡\n", "│ 20230601 ┆ 000001.SZ ┆ 0925 ┆ 11.6      ┆ … ┆ 417700.0   ┆ 4.84532e6   ┆ 11.6      ┆ 2023-06-01  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 09:25:00    │\n", "│ 20230601 ┆ 000001.SZ ┆ 0930 ┆ 11.6      ┆ … ┆ 1.510606e6 ┆ 1.7477944e7 ┆ 11.570154 ┆ 2023-06-01  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 09:30:00    │\n", "│ 20230601 ┆ 000001.SZ ┆ 0931 ┆ 11.56     ┆ … ┆ 1.072172e6 ┆ 1.2397453e7 ┆ 11.562933 ┆ 2023-06-01  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 09:31:00    │\n", "│ 20230601 ┆ 000001.SZ ┆ 0932 ┆ 11.57     ┆ … ┆ 1.4002e6   ┆ 1.6215498e7 ┆ 11.580844 ┆ 2023-06-01  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 09:32:00    │\n", "│ 20230601 ┆ 000001.SZ ┆ 0933 ┆ 11.6      ┆ … ┆ 518216.0   ┆ 6.011253e6  ┆ 11.599898 ┆ 2023-06-01  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 09:33:00    │\n", "│ …        ┆ …         ┆ …    ┆ …         ┆ … ┆ …          ┆ …           ┆ …         ┆ …           │\n", "│ 20230731 ┆ 688332.SH ┆ 1456 ┆ 69.199997 ┆ … ┆ 1303.0     ┆ 90172.09375 ┆ 69.203445 ┆ 2023-07-31  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 14:56:00    │\n", "│ 20230731 ┆ 688332.SH ┆ 1457 ┆ 69.204491 ┆ … ┆ 0.0        ┆ 0.0         ┆ 69.203445 ┆ 2023-07-31  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 14:57:00    │\n", "│ 20230731 ┆ 688332.SH ┆ 1458 ┆ 69.204491 ┆ … ┆ 0.0        ┆ 0.0         ┆ 69.203445 ┆ 2023-07-31  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 14:58:00    │\n", "│ 20230731 ┆ 688332.SH ┆ 1459 ┆ 69.204491 ┆ … ┆ 0.0        ┆ 0.0         ┆ 69.203445 ┆ 2023-07-31  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 14:59:00    │\n", "│ 20230731 ┆ 688332.SH ┆ 1500 ┆ 69.230003 ┆ … ┆ 8319.0     ┆ 575924.375  ┆ 69.230003 ┆ 2023-07-31  │\n", "│          ┆           ┆      ┆           ┆   ┆            ┆             ┆           ┆ 15:00:00    │\n", "└──────────┴───────────┴──────┴───────────┴───┴────────────┴─────────────┴───────────┴─────────────┘"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "from operators import PolarsOperators as ops\n", "\n", "class CONFIG:\n", "    DATA_DIR = \"/disk4/shared/intern/laiyc/minRawData/\"\n", "    PREPROCESS_DIR = \"/disk4/shared/intern/laiyc/preprocess/\"\n", "    RU_DIR = DATA_DIR + \"ru_min1/\"\n", "    DSH_COLS = ['date', 'symbol', 'hhmm', 'datetime']\n", "    SPEC_TIME = [\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]\n", "    TMP_DIR = \"/home/<USER>/data/disk/\"\n", "\n", "OHLCVA_vwap = pl.read_parquet(CONFIG.TMP_DIR + \"OHLCVA_vwap.parquet\")\n", "df = OHLCVA_vwap\n", "df"]}, {"cell_type": "code", "execution_count": 42, "id": "8902f0d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["shape: (1, 1)\n", "┌───────┐\n", "│ Close │\n", "│ ---   │\n", "│ u32   │\n", "╞═══════╡\n", "│ 0     │\n", "└───────┘\n", "shape: (1, 11)\n", "┌──────┬────────┬──────┬──────┬───┬────────┬────────┬──────┬──────────┐\n", "│ date ┆ symbol ┆ hhmm ┆ Open ┆ … ┆ Volume ┆ Amount ┆ Vwap ┆ datetime │\n", "│ ---  ┆ ---    ┆ ---  ┆ ---  ┆   ┆ ---    ┆ ---    ┆ ---  ┆ ---      │\n", "│ u32  ┆ u32    ┆ u32  ┆ u32  ┆   ┆ u32    ┆ u32    ┆ u32  ┆ u32      │\n", "╞══════╪════════╪══════╪══════╪═══╪════════╪════════╪══════╪══════════╡\n", "│ 0    ┆ 0      ┆ 0    ┆ 0    ┆ … ┆ 0      ┆ 0      ┆ 0    ┆ 0        │\n", "└──────┴────────┴──────┴──────┴───┴────────┴────────┴──────┴──────────┘\n"]}], "source": ["# df 填充策略：\n", "# 0. 确保Close没有Null和nan\n", "# 1. Volume, Amount缺了4行，直接填0\n", "# 2. Open, High, Low, Vwap 填成这一行的Close\n", "print(df.select(pl.col(\"Close\").is_null().sum()))\n", "df = df.with_columns([\n", "    pl.col(\"Volume\").fill_null(0.0),\n", "    pl.col(\"Amount\").fill_null(0.0),\n", "    pl.col(\"Open\").fill_null(pl.col(\"Close\")),\n", "    pl.col(\"High\").fill_null(pl.col(\"Close\")),\n", "    pl.col(\"Low\").fill_null(pl.col(\"Close\")),\n", "    pl.col(\"Vwap\").fill_null(pl.col(\"Close\")),\n", "])\n", "print(df.select([(pl.col(c).is_null().sum() ) for c in df.columns]))"]}, {"cell_type": "code", "execution_count": 8, "id": "a352a0fd", "metadata": {}, "outputs": [], "source": ["def factor_21(eps: float = 1e-8) -> pl.Expr:\n", "    vdiff = pl.col(\"Vwap\") - pl.col(\"Close\")\n", "    vsum  = pl.col(\"Vwap\") + pl.col(\"Close\")\n", "    return (\n", "        vdiff.rank().over([\"datetime\"])\n", "        / (vsum.rank().over([\"datetime\"]) + eps)\n", "    ).alias(\"factor_21\")"]}, {"cell_type": "code", "execution_count": 11, "id": "0aba9650", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (44_220_825, 1)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>factor_21</th></tr><tr><td>f64</td></tr></thead><tbody><tr><td>1.089712</td></tr><tr><td>1.476027</td></tr><tr><td>0.493157</td></tr><tr><td>0.347953</td></tr><tr><td>0.386297</td></tr><tr><td>&hellip;</td></tr><tr><td>0.514644</td></tr><tr><td>0.514644</td></tr><tr><td>0.514644</td></tr><tr><td>0.514644</td></tr><tr><td>0.520228</td></tr></tbody></table></div>"], "text/plain": ["shape: (44_220_825, 1)\n", "┌───────────┐\n", "│ factor_21 │\n", "│ ---       │\n", "│ f64       │\n", "╞═══════════╡\n", "│ 1.089712  │\n", "│ 1.476027  │\n", "│ 0.493157  │\n", "│ 0.347953  │\n", "│ 0.386297  │\n", "│ …         │\n", "│ 0.514644  │\n", "│ 0.514644  │\n", "│ 0.514644  │\n", "│ 0.514644  │\n", "│ 0.520228  │\n", "└───────────┘"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["s = df.select(factor_21())\n", "s"]}, {"cell_type": "code", "execution_count": 14, "id": "ba9a767a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (0, 1)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>MA20</th></tr><tr><td>f64</td></tr></thead><tbody></tbody></table></div>"], "text/plain": ["shape: (0, 1)\n", "┌──────┐\n", "│ MA20 │\n", "│ ---  │\n", "│ f64  │\n", "╞══════╡\n", "└──────┘"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["s = df.select(ops.rolling_rank(\"Close\", window=5, by_group=\"symbol\").alias(\"MA20\"))\n", "# nan count\n", "s.filter(pl.col(\"MA20\").is_null())\n"]}, {"cell_type": "code", "execution_count": null, "id": "53d89ecc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lyc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}