#!/usr/bin/env python3
"""
真实数据因子计算示例
直接使用你的真实数据进行因子计算
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import polars as pl
import numpy as np
import time
from operators import PolarsOperators as ops
from operators.config import load_data

def explore_data_structure():
    """探索真实数据结构"""
    print("=== 探索真实数据结构 ===")
    
    try:
        # 加载真实数据
        df = load_data()
        print(f"数据大小: {df.shape}")
        print(f"列名: {df.columns}")
        print(f"数据类型:")
        print(df.dtypes)
        
        print(f"\n股票数量: {len(df['symbol'].unique())}")
        print(f"股票样本: {df['symbol'].unique().head(10).to_list()}")
        
        if 'date' in df.columns:
            print(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
            print(f"日期样本: {df['date'].unique().head(10).to_list()}")
        
        if 'hhmm' in df.columns:
            print(f"时间范围: {df['hhmm'].min()} 到 {df['hhmm'].max()}")
            print(f"时间样本: {df['hhmm'].unique().head(10).to_list()}")
        
        print(f"\n数据样本:")
        print(df.head(10))
        
        print(f"\n数据统计:")
        numeric_cols = [col for col in df.columns if df[col].dtype in [pl.Float32, pl.Float64, pl.Int32, pl.Int64]]
        if numeric_cols:
            print(df.select(numeric_cols).describe())
        
        return df
        
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def calculate_basic_factors(df: pl.DataFrame, sample_size: int = 50000):
    """计算基础因子"""
    print(f"\n=== 计算基础因子 (样本大小: {sample_size}) ===")
    
    # 取样本数据以加快计算
    sample_df = df.head(sample_size)
    print(f"样本数据大小: {sample_df.shape}")
    
    # 方式1：直接使用算子 - 适合简单因子
    print("\n方式1：直接使用算子")
    start_time = time.time()
    
    result = sample_df.with_columns([
        # 基础动量因子
        ops.rolling_rank("Close", 20).alias("momentum_20"),
        ops.rolling_rank("Close", 60).alias("momentum_60"),
        
        # 价量关系因子
        ops.rolling_corr("Close", "Volume", 20).alias("price_vol_corr_20"),
        ops.rolling_cov("Close", "Volume", 20).alias("price_vol_cov_20"),
        ops.rolling_regbeta("Volume", "Close", 20).alias("vol_price_beta_20"),
        ops.rolling_regres("Volume", "Close", 20).alias("vol_price_resid_20"),
        
        # 趋势因子
        ops.decaylinear("Close", 10).alias("price_trend_10"),
        ops.decaylinear("Volume", 10).alias("volume_trend_10"),
        
        # 基础统计因子
        ops.rolling_mean("Close", 20).alias("price_ma_20"),
        ops.rolling_std("Close", 20).alias("price_std_20"),
    ])
    
    calc_time = time.time() - start_time
    print(f"计算时间: {calc_time:.3f}s")
    print(f"吞吐量: {len(sample_df)/calc_time:.0f} 行/秒")
    
    # 显示结果
    factor_cols = [
        "momentum_20", "momentum_60", "price_vol_corr_20", "price_vol_cov_20",
        "vol_price_beta_20", "vol_price_resid_20", "price_trend_10", 
        "volume_trend_10", "price_ma_20", "price_std_20"
    ]
    
    base_cols = ["symbol", "datetime"]
    if "date" in result.columns:
        base_cols.append("date")
    if "hhmm" in result.columns:
        base_cols.append("hhmm")
    base_cols.extend(["Close", "Volume"])
    
    sample_result = result.select(base_cols + factor_cols).head(15)
    print(f"\n因子计算结果样本:")
    print(sample_result)
    
    return result

def calculate_complex_factors(df: pl.DataFrame, sample_size: int = 50000):
    """计算复杂因子"""
    print(f"\n=== 计算复杂因子 ===")
    
    sample_df = df.head(sample_size)
    
    # 方式2：函数返回Expr - 适合复杂因子
    def factor_momentum_strength(short_window: int = 5, long_window: int = 20) -> pl.Expr:
        """动量强度因子"""
        short_momentum = ops.rolling_rank("Close", short_window)
        long_momentum = ops.rolling_rank("Close", long_window)
        return (short_momentum - long_momentum).alias(f"momentum_strength_{short_window}_{long_window}")
    
    def factor_volume_price_divergence(window: int = 20) -> pl.Expr:
        """量价背离因子"""
        price_rank = ops.rolling_rank("Close", window)
        volume_rank = ops.rolling_rank("Volume", window)
        return (price_rank - volume_rank).alias(f"vol_price_div_{window}")
    
    def factor_volatility_adjusted_momentum(window: int = 20) -> pl.Expr:
        """波动率调整动量因子"""
        momentum = ops.rolling_rank("Close", window)
        volatility = pl.col("Close").pct_change().rolling_std(window).over("symbol")
        return (momentum / (volatility + 1e-8)).alias(f"vol_adj_momentum_{window}")
    
    def factor_relative_position(window: int = 20) -> pl.Expr:
        """相对位置因子"""
        rolling_min = pl.col("Close").rolling_min(window).over("symbol")
        rolling_max = pl.col("Close").rolling_max(window).over("symbol")
        return (
            (pl.col("Close") - rolling_min) / (rolling_max - rolling_min + 1e-8)
        ).alias(f"relative_position_{window}")
    
    def factor_cross_sectional_rank(col: str, group_cols: list = None) -> pl.Expr:
        """截面排名因子"""
        if group_cols is None:
            if "date" in sample_df.columns and "hhmm" in sample_df.columns:
                group_cols = ["date", "hhmm"]
            else:
                group_cols = ["datetime"]
        return pl.col(col).rank().over(group_cols).alias(f"{col}_cs_rank")
    
    # 应用复杂因子
    start_time = time.time()
    
    result = sample_df.with_columns([
        factor_momentum_strength(5, 20),
        factor_volume_price_divergence(20),
        factor_volatility_adjusted_momentum(20),
        factor_relative_position(20),
        factor_cross_sectional_rank("Close"),
        factor_cross_sectional_rank("Volume"),
    ])
    
    calc_time = time.time() - start_time
    print(f"复杂因子计算时间: {calc_time:.3f}s")
    
    complex_factors = [
        "momentum_strength_5_20", "vol_price_div_20", "vol_adj_momentum_20",
        "relative_position_20", "Close_cs_rank", "Volume_cs_rank"
    ]
    
    base_cols = ["symbol", "datetime"]
    if "date" in result.columns:
        base_cols.append("date")
    if "hhmm" in result.columns:
        base_cols.append("hhmm")
    base_cols.extend(["Close", "Volume"])
    
    complex_sample = result.select(base_cols + complex_factors).head(15)
    print(f"复杂因子结果样本:")
    print(complex_sample)
    
    return result

def calculate_specific_time_factors(df: pl.DataFrame):
    """计算特定时间点因子"""
    print(f"\n=== 特定时间点因子 ===")
    
    # 检查是否有hhmm列
    if "hhmm" not in df.columns:
        print("数据中没有hhmm列，跳过特定时间点因子计算")
        return None
    
    # 定义特定时间点（开盘、中间、收盘等关键时点）
    spec_times = ["0930", "1000", "1030", "1100", "1330", "1400", "1430", "1500"]
    available_times = df["hhmm"].unique().to_list()
    valid_spec_times = [t for t in spec_times if t in available_times]
    
    print(f"可用的特定时间点: {valid_spec_times}")
    
    if not valid_spec_times:
        print("没有找到有效的特定时间点")
        return None
    
    # 在特定时间点计算因子
    spec_result = df.filter(
        pl.col("hhmm").is_in(valid_spec_times)
    ).with_columns([
        ops.rolling_rank("Close", 20).alias("spec_momentum_20"),
        ops.rolling_corr("Close", "Volume", 20).alias("spec_corr_20"),
        pl.col("Close").rank().over("datetime").alias("intraday_rank"),
    ])
    
    print(f"特定时间点数据大小: {spec_result.shape}")
    
    spec_sample = spec_result.select([
        "symbol", "date", "hhmm", "Close", "Volume",
        "spec_momentum_20", "spec_corr_20", "intraday_rank"
    ]).head(20)
    
    print("特定时间点因子样本:")
    print(spec_sample)
    
    return spec_result

def performance_benchmark(df: pl.DataFrame):
    """性能基准测试"""
    print(f"\n=== 性能基准测试 ===")
    
    # 测试不同数据规模的性能
    test_sizes = [10000, 50000, 100000, 500000]
    
    for size in test_sizes:
        if size > len(df):
            continue
            
        print(f"\n测试数据规模: {size:,} 行")
        test_df = df.head(size)
        
        start_time = time.time()
        result = test_df.with_columns([
            ops.rolling_rank("Close", 20).alias("momentum"),
            ops.rolling_corr("Close", "Volume", 20).alias("corr"),
            ops.rolling_regbeta("Volume", "Close", 20).alias("beta"),
        ])
        total_time = time.time() - start_time
        
        throughput = size / total_time
        print(f"  计算时间: {total_time:.3f}s")
        print(f"  吞吐量: {throughput:.0f} 行/秒")
        print(f"  平均每因子: {total_time/3:.3f}s")

def main():
    """主函数"""
    print("真实数据因子计算示例")
    print("=" * 60)
    
    # 探索数据结构
    df = explore_data_structure()
    if df is None:
        print("无法加载数据，退出")
        return
    
    # 计算基础因子
    basic_result = calculate_basic_factors(df, sample_size=44220825)
    
    # 计算复杂因子
    complex_result = calculate_complex_factors(df, sample_size=44220825)
    
    # 特定时间点因子
    spec_result = calculate_specific_time_factors(df)
    
    # 性能基准测试
    performance_benchmark(df)
    
    print("\n" + "=" * 60)
    print("真实数据因子计算完成！")
    print("\n使用建议:")
    print("1. 简单时序因子：直接使用 ops.rolling_rank() 等算子")
    print("2. 复杂组合因子：编写函数返回 pl.Expr，便于复用和维护")
    print("3. 截面因子：使用 .over(['date', 'hhmm']) 进行时间截面分组")
    print("4. 特定时点因子：先用 .filter() 筛选时间，再计算因子")
    print("5. 批量计算：在 .with_columns([...]) 中一次性计算多个因子以提高效率")

if __name__ == "__main__":
    main()
