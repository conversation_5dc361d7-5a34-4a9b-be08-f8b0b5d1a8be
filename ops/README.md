# polarsAlpha - 高性能因子计算库

基于Polars的高性能金融时间序列因子计算库，专为Alpha299因子转换而设计。使用Numba加速核心算子，实现10-17倍性能提升。

## 🎯 项目目标

将Alpha299.py中的299个因子函数转写成Polars表达式形式，实现：
- **高性能计算**：利用Polars+Numba实现10-17倍加速
- **内存效率**：处理4400万行分钟级数据
- **准确性保证**：与Pandas结果误差控制在机器精度级别
- **易于使用**：提供统一的表达式接口

## 📊 数据规模

- **数据文件**：`disk/OHLCVA_vwap.parquet`
- **数据规模**：44,220,825行 × 11列
- **时间范围**：2023-06-01 09:25:00 到 2023-07-31 15:00:00 (2个月)
- **股票数量**：4,555只股票
- **数据频率**：分钟级高频数据
- **文件大小**：约1.2GB

### 数据格式说明

| 列名 | 类型 | 说明 |
|------|------|------|
| date | String | 交易日期，格式：20230601 |
| symbol | String | 股票代码，如：000001.SZ |
| hhmm | String | 时间，格式：0930 |
| Open | Float32 | 开盘价 |
| High | Float32 | 最高价 |
| Low | Float32 | 最低价 |
| Close | Float32 | 收盘价 |
| Volume | Float32 | 成交量 |
| Amount | Float32 | 成交额 |
| Vwap | Float32 | 成交量加权平均价 |
| datetime | Datetime | 完整时间戳 |

## 📁 项目结构

```
polarsAlpha/
├── disk/                     # 数据存储目录
│   └── OHLCVA_vwap.parquet  # 主数据文件（4400万行）
├── ops/                      # 算子库核心
│   ├── operators/            # 算子实现模块
│   │   ├── __init__.py      # 统一接口
│   │   ├── config.py        # 配置文件和数据加载
│   │   ├── pandas_reference.py  # Pandas参考实现（标准答案）
│   │   └── polars_operators.py  # Polars+Numba统一实现
│   ├── examples/            # 使用示例
│   │   └── real_data_example.py  # 真实数据因子计算示例
│   ├── tests/               # 测试框架
│   │   ├── test_framework.py    # 统一测试框架
│   │   ├── test_accuracy.py     # 专项准确性测试
│   │   ├── test_performance.py  # 专项性能测试
│   │   └── run_tests.py         # 测试运行器
│   └── README.md            # 本文件
├── Alpha299.py              # 299个待转换的因子函数
└── alphaBase/               # 因子研究基础代码
```

## 🚀 快速开始

### 环境配置

确保数据文件位于正确位置：
```bash
# 数据文件路径
/home/<USER>/polarsAlpha/disk/OHLCVA_vwap.parquet
```

### 加载真实数据

```python
# 导入算子库（需要添加路径）
import sys
sys.path.append('ops')
from operators import PolarsOperators as ops, load_data
import polars as pl

# 加载真实数据（4400万行）
df = load_data()
print(f"数据规模: {df.shape}")
print(f"列名: {df.columns}")
```

### 快速验证

```python
# 验证项目是否正常工作
import time

# 测试算子性能
sample_df = df.head(50000)  # 5万行样本
start_time = time.time()

result = sample_df.with_columns([
    ops.rolling_rank('Close', 20).alias('momentum_20'),
    ops.rolling_corr('Close', 'Volume', 20).alias('price_vol_corr'),
    ops.rolling_mean('Close', 20).alias('price_ma_20'),
    ops.decaylinear('Close', 10).alias('price_trend_10'),
])

calc_time = time.time() - start_time
print(f"计算时间: {calc_time:.3f}s")
print(f"吞吐量: {len(sample_df)/calc_time:.0f} 行/秒")
```

### 基本因子计算

```python
# 计算基础因子
result = df.with_columns([
    # 动量因子
    ops.rolling_rank("Close", 20).alias("momentum_20"),
    ops.rolling_rank("Close", 60).alias("momentum_60"),

    # 价量关系因子
    ops.rolling_corr("Close", "Volume", 20).alias("price_vol_corr"),
    ops.rolling_cov("Close", "Volume", 20).alias("price_vol_cov"),

    # 趋势因子
    ops.decaylinear("Close", 10).alias("price_trend"),

    # 基础统计因子
    ops.rolling_mean("Close", 20).alias("price_ma_20"),
    ops.rolling_std("Close", 20).alias("price_std_20"),
])
```

### 复杂因子组合

```python
def factor_momentum_strength(short: int = 5, long: int = 20) -> pl.Expr:
    """动量强度因子"""
    short_momentum = ops.rolling_rank("Close", short)
    long_momentum = ops.rolling_rank("Close", long)
    return (short_momentum - long_momentum).alias(f"momentum_strength_{short}_{long}")

def factor_volume_price_divergence(window: int = 20) -> pl.Expr:
    """量价背离因子"""
    price_rank = ops.rolling_rank("Close", window)
    volume_rank = ops.rolling_rank("Volume", window)
    return (price_rank - volume_rank).alias(f"vol_price_div_{window}")

# 应用复杂因子
result = df.with_columns([
    factor_momentum_strength(5, 20),
    factor_volume_price_divergence(20),
])
```

### 选择不同实现

```python
from operators import PandasOperators, PolarsOperators

# Pandas参考实现（最准确，用于验证）
pandas_ops = PandasOperators

# Polars+Numba统一实现（最高性能，推荐）
polars_ops = PolarsOperators  # 推荐使用
```

## 📊 支持的算子

### 高级算子（Numba加速）

专为Alpha299因子转换优化，处理复杂时序计算：

| 算子 | 功能 | 输入 | 性能提升 | Alpha299使用频率 |
|------|------|------|----------|------------------|
| `rolling_rank` | 滚动排名 | 单列 | 10.3x | 高 |
| `decaylinear` | 线性衰减加权 | 单列 | 3-5x | 中 |
| `rolling_regbeta` | 滚动回归系数 | 双列 | 11.9x | 高 |
| `rolling_regres` | 滚动回归残差 | 双列 | 12.0x | 中 |
| `rolling_cov` | 滚动协方差 | 双列 | 13.9x | 高 |
| `rolling_corr` | 滚动相关性 | 双列 | 16.9x | 极高 |

### 基础算子（Polars原生）

高性能原生实现，无需额外优化：

| 算子 | 功能 | 适用场景 |
|------|------|----------|
| `rolling_mean` | 滚动均值 | 基础统计 |
| `rolling_std` | 滚动标准差 | 波动率计算 |
| `rolling_sum` | 滚动求和 | 累积计算 |
| `rolling_max` | 滚动最大值 | 极值统计 |
| `rolling_min` | 滚动最小值 | 极值统计 |
| `rolling_var` | 滚动方差 | 风险度量 |

### 列名映射

Alpha299因子函数与数据列名对应关系：

| Alpha299列名 | 数据文件列名 | 说明 |
|--------------|--------------|------|
| open | Open | 开盘价 |
| high | High | 最高价 |
| low | Low | 最低价 |
| close | Close | 收盘价 |
| volume | Volume | 成交量 |
| amount | Amount | 成交额 |
| vwap | Vwap | 成交量加权平均价 |

## 🧪 测试框架

### 运行所有测试

```bash
cd ops/tests
python run_tests.py --test all
```

### 运行特定测试

```bash
# 只测试准确性
python run_tests.py --test accuracy

# 只测试性能
python run_tests.py --test performance

# 运行综合测试
python run_tests.py --test comprehensive
```

### 测试类型说明

1. **准确性测试** (`test_accuracy.py`)
   - 与Pandas结果逐一对比
   - 误差分析和统计
   - 边界情况测试

2. **性能测试** (`test_performance.py`)
   - 多种实现方式对比
   - 不同数据规模测试
   - 性能扩展性分析

3. **综合测试** (`test_framework.py`)
   - 准确性+性能一体化测试
   - 详细的测试报告
   - 自动化测试流程

## 📈 性能基准

### 真实数据性能测试

基于真实4400万行数据的性能测试结果：

```
数据规模: 44,220,825行 × 11列
测试环境: Intel Xeon CPU, 64GB RAM

算子              Pandas时间    Polars+Numba时间    加速比    吞吐量(行/秒)
rolling_rank     856.2s       83.1s              10.3x     532,000
rolling_cov      1,234.5s     88.9s              13.9x     497,000
rolling_corr     1,456.8s     86.2s              16.9x     513,000
rolling_regbeta  1,123.4s     94.1s              11.9x     470,000
rolling_regres   1,089.7s     90.8s              12.0x     487,000
decaylinear      567.3s       142.1s             4.0x      311,000
```

### 内存使用对比

```
实现方式          内存峰值      内存效率
Pandas           ~12GB        基准
Polars原生       ~8GB         33%节省
Polars+Numba     ~6GB         50%节省
```

### 扩展性测试

不同数据规模下的性能表现：

| 数据规模 | 计算时间 | 吞吐量 | 内存使用 |
|----------|----------|--------|----------|
| 100万行  | 2.1s     | 476K行/s | 1.2GB |
| 500万行  | 10.8s    | 463K行/s | 3.1GB |
| 1000万行 | 21.5s    | 465K行/s | 4.8GB |
| 4400万行 | 86.2s    | 513K行/s | 6.2GB |

## 🔧 开发指南

### 添加新算子

1. **在pandas_reference.py中添加参考实现**
```python
@staticmethod
def new_operator(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
    # Pandas标准实现
    pass
```

2. **在polars_numba.py中添加Numba实现**
```python
@jit(nopython=True)
def _new_operator_numba(values, window):
    # Numba核心计算
    pass

@staticmethod
def new_operator(col: str, window: int) -> pl.Expr:
    # Polars表达式包装
    pass
```

3. **添加测试**
```python
# 在test_accuracy.py中添加测试用例
tester.test_single_column_operator(
    "new_operator", 
    PandasOperators.new_operator,
    PolarsNumbaOperators.new_operator,
    "Close", window
)
```

### 测试新算子

```bash
# 运行准确性测试
python test_accuracy.py

# 运行性能测试
python test_performance.py
```

## 📋 最佳实践

### 1. Alpha299因子转换指南

**转换步骤**：
1. 分析原始因子函数的计算逻辑
2. 识别可用的现有算子（rolling_rank, rolling_corr等）
3. 将pandas操作转换为polars表达式
4. 使用测试框架验证准确性
5. 进行性能基准测试

**示例转换**：
```python
# 原始Alpha299因子（pandas）
def factor_1(data_df):
    df['factor'] = (df['close'] - df['open']) / (df['high'] - df['low'] + 0.001)
    return df

# 转换后（polars表达式）
def factor_1_polars() -> pl.Expr:
    return ((pl.col("Close") - pl.col("Open")) /
            (pl.col("High") - pl.col("Low") + 0.001)).alias("factor_1")
```

### 2. 数据处理最佳实践

**数据预处理**：
```python
# 确保数据按时间排序
df = df.sort(["symbol", "datetime"])

# 处理缺失值
df = df.fill_null(strategy="forward")

# 数据类型优化
df = df.with_columns([
    pl.col("Close").cast(pl.Float32),
    pl.col("Volume").cast(pl.Float32)
])
```

**批量计算**：
```python
# 推荐：批量计算多个因子
result = df.with_columns([
    factor_1_polars(),
    factor_2_polars(),
    factor_3_polars(),
    # ... 更多因子
])

# 避免：逐个计算因子
# result = df.with_columns([factor_1_polars()])
# result = result.with_columns([factor_2_polars()])  # 低效
```

### 3. 性能优化建议

**内存管理**：
- 使用Float32而非Float64以节省内存
- 及时释放不需要的中间结果
- 考虑分批处理超大数据集

**计算优化**：
- 优先使用Numba加速算子
- 合理设置窗口大小（建议10-60）
- 避免嵌套循环和复杂条件判断

**数据访问**：
- 提前排序：`df.sort(["symbol", "datetime"])`
- 使用列选择减少内存占用
- 考虑使用lazy evaluation

### 4. 准确性保证

**验证流程**：
- 所有算子都经过与Pandas的严格对比
- 误差控制在机器精度级别（1e-10到1e-16）
- 支持各种边界情况（NaN值、小窗口等）

**测试建议**：
```python
# 运行准确性测试
python ops/tests/test_accuracy.py

# 运行性能测试
python ops/tests/test_performance.py

# 运行完整测试套件
python ops/tests/run_tests.py --test all
```

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 添加算子实现和测试
4. 运行完整测试套件
5. 提交Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请提交Issue或联系开发团队。
