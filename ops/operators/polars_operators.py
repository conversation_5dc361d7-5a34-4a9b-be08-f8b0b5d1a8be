"""
Polars算子统一实现
整合了Numba加速和原生实现，提供最优性能和准确性
"""

import polars as pl
import numpy as np
from numba import jit, prange
from numpy.lib.stride_tricks import sliding_window_view

# ============================================================================
# Numba加速函数
# ============================================================================

#@jit(nopython=True)
def _rolling_rank_numba(values, window):
    n = values.size
    out = np.empty(n, dtype=np.float64)

    # 先处理满窗部分
    sw = sliding_window_view(values, window)              # (n-w+1, w)
    ref = sw[:, -1][:, None]
    out[window-1:] = (sw <= ref).sum(axis=1) / window

    # 处理前 w-1 行
    for i in range(window-1):
        sub = values[:i+1]
        out[i] = (sub <= values[i]).sum() / sub.size

    return out

@jit(nopython=True, parallel=True, fastmath=True)
def _rolling_rank_numba_optimized(values, window):
    """优化的Numba加速滚动排名计算 - 并行优化版本"""
    n = len(values)
    result = np.empty(n, dtype=np.float64)

    # 使用prange实现并行计算
    for i in prange(n):
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        window_size = end_idx - start_idx

        if window_size == 0:
            result[i] = np.nan
        else:
            current_val = values[i]
            # 计算排名：有多少个值小于等于当前值 - 避免数组切片
            rank_sum = 0.0
            for j in range(start_idx, end_idx):
                if values[j] <= current_val:
                    rank_sum += 1.0

            # 转换为百分比排名
            result[i] = rank_sum / window_size

    return result

@jit(nopython=True)
def _decaylinear_numba(values, window):
    """Numba加速的线性衰减加权平均（原版本，用于一致性验证）"""
    n = len(values)
    result = np.empty(n, dtype=np.float64)

    for i in range(n):
        start_idx = max(0, i - window + 1)
        window_vals = values[start_idx:i+1]

        if len(window_vals) == 0:
            result[i] = np.nan
        else:
            # 线性权重：1, 2, 3, ..., len(window_vals)
            weights = np.arange(1, len(window_vals) + 1, dtype=np.float64)
            weighted_sum = np.sum(window_vals * weights)
            weight_sum = np.sum(weights)
            result[i] = weighted_sum / weight_sum

    return result

@jit(nopython=True, parallel=True, fastmath=True)
def _decaylinear_numba_optimized(values, window):
    """优化的Numba加速线性衰减加权平均 - 并行优化版本"""
    n = len(values)
    result = np.empty(n, dtype=np.float64)

    # 使用prange实现并行计算
    for i in prange(n):
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        window_size = end_idx - start_idx

        if window_size == 0:
            result[i] = np.nan
        else:
            # 线性权重计算 - 避免数组切片和np.arange
            weighted_sum = 0.0
            weight_sum = 0.0

            for j in range(window_size):
                weight = j + 1  # 权重从1开始
                weighted_sum += values[start_idx + j] * weight
                weight_sum += weight

            result[i] = weighted_sum / weight_sum

    return result

@jit(nopython=True, parallel=True, fastmath=True)
def _rolling_cov_numba(x_values, y_values, window):
    """Numba加速的滚动协方差 - 并行优化版本"""
    n = len(x_values)
    result = np.empty(n, dtype=np.float64)

    # 使用prange实现并行计算
    for i in prange(n):
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        window_size = end_idx - start_idx

        if window_size < 2:
            result[i] = np.nan
        else:
            # 计算均值 - 避免数组切片，直接索引计算
            x_mean = 0.0
            y_mean = 0.0
            for j in range(start_idx, end_idx):
                x_mean += x_values[j]
                y_mean += y_values[j]
            x_mean /= window_size
            y_mean /= window_size

            # 计算协方差
            cov_sum = 0.0
            for j in range(start_idx, end_idx):
                cov_sum += (x_values[j] - x_mean) * (y_values[j] - y_mean)

            # 使用ddof=1（无偏估计）
            result[i] = cov_sum / (window_size - 1)

    return result

@jit(nopython=True, parallel=True, fastmath=True)
def _rolling_corr_numba(x_values, y_values, window):
    """Numba加速的滚动相关性 - 并行优化版本"""
    n = len(x_values)
    result = np.empty(n, dtype=np.float64)

    # 使用prange实现并行计算
    for i in prange(n):
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        window_size = end_idx - start_idx

        if window_size < 2:
            result[i] = np.nan
        else:
            # 计算均值 - 避免数组切片
            x_mean = 0.0
            y_mean = 0.0
            for j in range(start_idx, end_idx):
                x_mean += x_values[j]
                y_mean += y_values[j]
            x_mean /= window_size
            y_mean /= window_size

            # 计算协方差和方差
            cov_sum = 0.0
            x_var_sum = 0.0
            y_var_sum = 0.0

            for j in range(start_idx, end_idx):
                x_diff = x_values[j] - x_mean
                y_diff = y_values[j] - y_mean
                cov_sum += x_diff * y_diff
                x_var_sum += x_diff * x_diff
                y_var_sum += y_diff * y_diff

            x_std = np.sqrt(x_var_sum / (window_size - 1))
            y_std = np.sqrt(y_var_sum / (window_size - 1))

            if x_std == 0.0 or y_std == 0.0:
                result[i] = np.nan
            else:
                result[i] = (cov_sum / (window_size - 1)) / (x_std * y_std)

    return result

@jit(nopython=True, parallel=True, fastmath=True)
def _rolling_regbeta_numba(x_values, y_values, window):
    """Numba加速的滚动回归beta系数 - 并行优化版本"""
    n = len(x_values)
    result = np.empty(n, dtype=np.float64)

    # 使用prange实现并行计算
    for i in prange(n):
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        window_size = end_idx - start_idx

        if window_size < 2:
            result[i] = np.nan
        else:
            # 计算均值 - 避免数组切片
            x_mean = 0.0
            y_mean = 0.0
            for j in range(start_idx, end_idx):
                x_mean += x_values[j]
                y_mean += y_values[j]
            x_mean /= window_size
            y_mean /= window_size

            # 计算beta = Cov(X,Y) / Var(X)
            cov_sum = 0.0
            var_sum = 0.0

            for j in range(start_idx, end_idx):
                x_diff = x_values[j] - x_mean
                y_diff = y_values[j] - y_mean
                cov_sum += x_diff * y_diff
                var_sum += x_diff * x_diff

            if var_sum == 0.0:
                result[i] = np.nan
            else:
                # 使用ddof=1保持一致性
                cov_xy = cov_sum / (window_size - 1)
                var_x = var_sum / (window_size - 1)
                result[i] = cov_xy / var_x

    return result

@jit(nopython=True, parallel=True, fastmath=True)
def _rolling_regres_numba(x_values, y_values, window):
    """Numba加速的滚动回归残差 - 并行优化版本"""
    n = len(x_values)
    result = np.empty(n, dtype=np.float64)

    # 使用prange实现并行计算
    for i in prange(n):
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        window_size = end_idx - start_idx

        if window_size < 2:
            result[i] = np.nan
        else:
            # 计算均值 - 避免数组切片
            x_mean = 0.0
            y_mean = 0.0
            for j in range(start_idx, end_idx):
                x_mean += x_values[j]
                y_mean += y_values[j]
            x_mean /= window_size
            y_mean /= window_size

            # 计算回归系数
            cov_sum = 0.0
            var_sum = 0.0

            for j in range(start_idx, end_idx):
                x_diff = x_values[j] - x_mean
                y_diff = y_values[j] - y_mean
                cov_sum += x_diff * y_diff
                var_sum += x_diff * x_diff

            if var_sum == 0.0:
                result[i] = np.nan
            else:
                # 使用ddof=1保持一致性
                beta = (cov_sum / (window_size - 1)) / (var_sum / (window_size - 1))
                alpha = y_mean - beta * x_mean

                # 计算当前点的残差
                predicted = alpha + beta * x_values[i]
                result[i] = y_values[i] - predicted

    return result

# ============================================================================
# Polars算子类
# ============================================================================

class PolarsOperators:
    """Polars算子统一实现 - 使用最优的实现方式"""
    
    # 单列算子（Numba加速）
    @staticmethod
    def rolling_rank(col_or_expr, window: int, optimized: bool = True) -> pl.Expr:
        """滚动排名 - Numba加速版本（针对单一时序）

        Args:
            col_or_expr: 列名或表达式
            window: 窗口大小
            optimized: 是否使用优化版本（启用多核并行）
        """
        def rolling_rank_fast(s: pl.Series) -> pl.Series:
            values = s.to_numpy()
            if optimized:
                result = _rolling_rank_numba_optimized(values, window)
            else:
                result = _rolling_rank_numba(values, window)
            return pl.Series(result, dtype=pl.Float64)

        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.map_batches(rolling_rank_fast)
    
    @staticmethod
    def decaylinear(col_or_expr, window: int, optimized: bool = True) -> pl.Expr:
        """线性衰减加权平均 - Numba加速版本（针对单一时序）

        Args:
            col_or_expr: 列名或表达式
            window: 窗口大小
            optimized: 是否使用优化版本（启用多核并行）
        """
        def decaylinear_fast(s: pl.Series) -> pl.Series:
            values = s.to_numpy()
            if optimized:
                result = _decaylinear_numba_optimized(values, window)
            else:
                result = _decaylinear_numba(values, window)
            return pl.Series(result, dtype=pl.Float64)

        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.map_batches(decaylinear_fast)
    
    # 双列算子（混合实现：原生优先，Numba备用）
    @staticmethod
    def rolling_cov(col_x_or_expr, col_y_or_expr, window: int, use_native: bool = False) -> pl.Expr:
        """滚动协方差 - 支持原生和Numba两种实现

        Args:
            col_x_or_expr: X列名或表达式
            col_y_or_expr: Y列名或表达式
            window: 窗口大小
            use_native: 是否使用Polars原生实现（推荐）
        """
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_x_or_expr, str):
            expr_x = pl.col(col_x_or_expr)
        else:
            expr_x = col_x_or_expr

        if isinstance(col_y_or_expr, str):
            expr_y = pl.col(col_y_or_expr)
        else:
            expr_y = col_y_or_expr

        if use_native:
            # 使用Polars原生rolling_cov函数，设置min_periods=2与Numba实现对齐
            return pl.rolling_cov(expr_x, expr_y, window_size=window, ddof=1, min_periods=2)
        else:
            # 使用Numba实现（用于一致性验证）
            def cov_fast(struct_series: pl.Series) -> pl.Series:
                df_batch = struct_series.struct.unnest()
                x_values = df_batch.to_series(0).to_numpy()
                y_values = df_batch.to_series(1).to_numpy()
                result = _rolling_cov_numba(x_values, y_values, window)
                return pl.Series(result, dtype=pl.Float64)

            return pl.struct([expr_x, expr_y]).map_batches(cov_fast)
    
    @staticmethod
    def rolling_corr(col_x_or_expr, col_y_or_expr, window: int, use_native: bool = False) -> pl.Expr:
        """滚动相关性 - 支持原生和Numba两种实现

        Args:
            col_x_or_expr: X列名或表达式
            col_y_or_expr: Y列名或表达式
            window: 窗口大小
            use_native: 是否使用Polars原生实现（推荐）
        """
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_x_or_expr, str):
            expr_x = pl.col(col_x_or_expr)
        else:
            expr_x = col_x_or_expr

        if isinstance(col_y_or_expr, str):
            expr_y = pl.col(col_y_or_expr)
        else:
            expr_y = col_y_or_expr

        if use_native:
            # 使用Polars原生rolling_corr函数，设置min_periods=2与Numba实现对齐
            return pl.rolling_corr(expr_x, expr_y, window_size=window, ddof=1, min_periods=2)
        else:
            # 使用Numba实现（用于一致性验证）
            def corr_fast(struct_series: pl.Series) -> pl.Series:
                df_batch = struct_series.struct.unnest()
                x_values = df_batch.to_series(0).to_numpy()
                y_values = df_batch.to_series(1).to_numpy()
                result = _rolling_corr_numba(x_values, y_values, window)
                return pl.Series(result, dtype=pl.Float64)

            return pl.struct([expr_x, expr_y]).map_batches(corr_fast)
    
    @staticmethod
    def rolling_regbeta(col_x_or_expr, col_y_or_expr, window: int, use_native: bool = False) -> pl.Expr:
        """滚动回归beta系数 - 支持原生和Numba两种实现

        Args:
            col_x_or_expr: X列名或表达式（自变量）
            col_y_or_expr: Y列名或表达式（因变量）
            window: 窗口大小
            use_native: 是否使用Polars原生实现（推荐）

        公式：beta = cov(x,y) / var(x)
        """
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_x_or_expr, str):
            expr_x = pl.col(col_x_or_expr)
        else:
            expr_x = col_x_or_expr

        if isinstance(col_y_or_expr, str):
            expr_y = pl.col(col_y_or_expr)
        else:
            expr_y = col_y_or_expr

        if use_native:
            # 使用Polars原生函数计算：beta = cov(x,y) / var(x)，设置min_periods=2与Numba实现对齐
            cov_xy = pl.rolling_cov(expr_x, expr_y, window_size=window, ddof=1, min_periods=2)
            var_x = expr_x.rolling_var(window_size=window, ddof=1, min_periods=2)
            return (cov_xy / var_x).alias("rolling_regbeta")
        else:
            # 使用Numba实现（用于一致性验证）
            def regbeta_fast(struct_series: pl.Series) -> pl.Series:
                df_batch = struct_series.struct.unnest()
                x_values = df_batch.to_series(0).to_numpy()
                y_values = df_batch.to_series(1).to_numpy()
                result = _rolling_regbeta_numba(x_values, y_values, window)
                return pl.Series(result, dtype=pl.Float64)

            return pl.struct([expr_x, expr_y]).map_batches(regbeta_fast)
    
    @staticmethod
    def rolling_regres(col_x_or_expr, col_y_or_expr, window: int, use_native: bool = False) -> pl.Expr:
        """滚动回归残差 - 支持原生和Numba两种实现

        Args:
            col_x_or_expr: X列名或表达式（自变量）
            col_y_or_expr: Y列名或表达式（因变量）
            window: 窗口大小
            use_native: 是否使用Polars原生实现（推荐）

        公式：resid = y - (alpha + beta * x)
        其中：alpha = mean(y) - beta * mean(x)
              beta = cov(x,y) / var(x)
        """
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_x_or_expr, str):
            expr_x = pl.col(col_x_or_expr)
        else:
            expr_x = col_x_or_expr

        if isinstance(col_y_or_expr, str):
            expr_y = pl.col(col_y_or_expr)
        else:
            expr_y = col_y_or_expr

        if use_native:
            # 注意：rolling_regres的原生实现比较复杂，因为需要计算每个点相对于其窗口回归线的残差
            # 这里我们暂时使用Numba实现来保证一致性，未来可以考虑更复杂的原生实现
            def regres_fast(struct_series: pl.Series) -> pl.Series:
                df_batch = struct_series.struct.unnest()
                x_values = df_batch.to_series(0).to_numpy()
                y_values = df_batch.to_series(1).to_numpy()
                result = _rolling_regres_numba(x_values, y_values, window)
                return pl.Series(result, dtype=pl.Float64)

            return pl.struct([expr_x, expr_y]).map_batches(regres_fast)
        else:
            # 使用Numba实现（用于一致性验证）
            def regres_fast(struct_series: pl.Series) -> pl.Series:
                df_batch = struct_series.struct.unnest()
                x_values = df_batch.to_series(0).to_numpy()
                y_values = df_batch.to_series(1).to_numpy()
                result = _rolling_regres_numba(x_values, y_values, window)
                return pl.Series(result, dtype=pl.Float64)

            return pl.struct([expr_x, expr_y]).map_batches(regres_fast)
    
    # 基础算子（Polars原生，性能优异）
    @staticmethod
    def rolling_mean(col_or_expr, window: int) -> pl.Expr:
        """滚动均值 - Polars原生（针对单一时序）"""
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.rolling_mean(window, min_periods=1).cast(pl.Float64)
    
    @staticmethod
    def rolling_std(col_or_expr, window: int) -> pl.Expr:
        """滚动标准差 - Polars原生（针对单一时序）"""
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.rolling_std(window, min_periods=1).cast(pl.Float64)

    @staticmethod
    def rolling_sum(col_or_expr, window: int) -> pl.Expr:
        """滚动求和 - Polars原生（针对单一时序）"""
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.rolling_sum(window, min_periods=1).cast(pl.Float64)

    @staticmethod
    def rolling_max(col_or_expr, window: int) -> pl.Expr:
        """滚动最大值 - Polars原生（针对单一时序）"""
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.rolling_max(window, min_periods=1).cast(pl.Float64)

    @staticmethod
    def rolling_min(col_or_expr, window: int) -> pl.Expr:
        """滚动最小值 - Polars原生（针对单一时序）"""
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.rolling_min(window, min_periods=1).cast(pl.Float64)

    @staticmethod
    def rolling_var(col_or_expr, window: int) -> pl.Expr:
        """滚动方差 - Polars原生（针对单一时序）"""
        # 处理输入参数：支持字符串列名或表达式
        if isinstance(col_or_expr, str):
            expr = pl.col(col_or_expr)
        else:
            expr = col_or_expr

        return expr.rolling_var(window, min_periods=1).cast(pl.Float64)
