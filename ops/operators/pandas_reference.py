"""
Pandas参考实现 - 作为准确性验证的标准
"""

import pandas as pd
import numpy as np
from typing import Union

class PandasOperators:
    """Pandas参考实现，作为标准答案"""
    
    @staticmethod
    def rolling_rank(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """Pandas滚动排名实现"""
        def rank_last(x):
            if len(x) == 0:
                return np.nan
            return (x <= x.iloc[-1]).sum() / len(x)
        
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).apply(rank_last, raw=False).reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).apply(rank_last, raw=False)
    
    @staticmethod
    def decaylinear(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """Pandas线性衰减加权平均"""
        def linear_decay(x):
            if len(x) == 0:
                return np.nan
            weights = np.arange(1, len(x) + 1)
            return np.average(x, weights=weights)
        
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).apply(linear_decay, raw=True).reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).apply(linear_decay, raw=True)
    
    @staticmethod
    def rolling_regbeta(df: pd.DataFrame, col_x: str, col_y: str, window: int, by_group: str = None) -> pd.Series:
        """Pandas滚动回归beta"""
        result = []
        
        if by_group:
            groups = df.groupby(by_group)
        else:
            groups = [(None, df)]
        
        for group_name, group_df in groups:
            group_result = []
            
            for i in range(len(group_df)):
                start_idx = max(0, i - window + 1)
                window_data = group_df.iloc[start_idx:i+1]
                
                if len(window_data) < 2:
                    group_result.append(np.nan)
                else:
                    x = window_data[col_x].values
                    y = window_data[col_y].values
                    x_mean, y_mean = x.mean(), y.mean()
                    numerator = ((x - x_mean) * (y - y_mean)).sum()
                    denominator = ((x - x_mean) ** 2).sum()
                    beta = numerator / denominator if denominator != 0 else np.nan
                    group_result.append(beta)
            
            result.extend(group_result)
        
        return pd.Series(result, index=df.index)
    
    @staticmethod
    def rolling_regres(df: pd.DataFrame, col_x: str, col_y: str, window: int, by_group: str = None) -> pd.Series:
        """Pandas滚动回归残差"""
        result = []
        
        if by_group:
            groups = df.groupby(by_group)
        else:
            groups = [(None, df)]
        
        for group_name, group_df in groups:
            group_result = []
            
            for i in range(len(group_df)):
                start_idx = max(0, i - window + 1)
                window_data = group_df.iloc[start_idx:i+1]
                
                if len(window_data) < 2:
                    group_result.append(np.nan)
                else:
                    x = window_data[col_x].values
                    y = window_data[col_y].values
                    x_mean, y_mean = x.mean(), y.mean()
                    numerator = ((x - x_mean) * (y - y_mean)).sum()
                    denominator = ((x - x_mean) ** 2).sum()
                    
                    if denominator == 0:
                        group_result.append(np.nan)
                    else:
                        beta = numerator / denominator
                        alpha = y_mean - beta * x_mean
                        current_x = group_df.iloc[i][col_x]
                        current_y = group_df.iloc[i][col_y]
                        residual = current_y - (alpha + beta * current_x)
                        group_result.append(residual)
            
            result.extend(group_result)
        
        return pd.Series(result, index=df.index)
    
    @staticmethod
    def rolling_cov(df: pd.DataFrame, col_x: str, col_y: str, window: int, by_group: str = None) -> pd.Series:
        """Pandas滚动协方差"""
        result = []
        
        if by_group:
            groups = df.groupby(by_group)
        else:
            groups = [(None, df)]
        
        for group_name, group_df in groups:
            group_result = []
            
            for i in range(len(group_df)):
                start_idx = max(0, i - window + 1)
                window_data = group_df.iloc[start_idx:i+1]
                
                if len(window_data) < 2:
                    group_result.append(np.nan)
                else:
                    x = window_data[col_x].values
                    y = window_data[col_y].values
                    # 使用ddof=1的协方差计算（与pandas一致）
                    cov = np.cov(x, y, ddof=1)[0, 1]
                    group_result.append(cov)
            
            result.extend(group_result)
        
        return pd.Series(result, index=df.index)
    
    @staticmethod
    def rolling_corr(df: pd.DataFrame, col_x: str, col_y: str, window: int, by_group: str = None) -> pd.Series:
        """Pandas滚动相关性"""
        result = []
        
        if by_group:
            groups = df.groupby(by_group)
        else:
            groups = [(None, df)]
        
        for group_name, group_df in groups:
            group_result = []
            
            for i in range(len(group_df)):
                start_idx = max(0, i - window + 1)
                window_data = group_df.iloc[start_idx:i+1]
                
                if len(window_data) < 2:
                    group_result.append(np.nan)
                else:
                    x = window_data[col_x].values
                    y = window_data[col_y].values
                    corr = np.corrcoef(x, y)[0, 1]
                    group_result.append(corr)
            
            result.extend(group_result)
        
        return pd.Series(result, index=df.index)
    
    # 基础算子
    @staticmethod
    def rolling_mean(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """滚动均值"""
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).mean().reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).mean()
    
    @staticmethod
    def rolling_std(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """滚动标准差"""
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).std().reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).std()
    
    @staticmethod
    def rolling_sum(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """滚动求和"""
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).sum().reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).sum()
    
    @staticmethod
    def rolling_max(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """滚动最大值"""
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).max().reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).max()
    
    @staticmethod
    def rolling_min(df: pd.DataFrame, col: str, window: int, by_group: str = None) -> pd.Series:
        """滚动最小值"""
        if by_group:
            return df.groupby(by_group)[col].rolling(window, min_periods=1).min().reset_index(0, drop=True)
        else:
            return df[col].rolling(window, min_periods=1).min()
    
    @staticmethod
    def rank(df: pd.DataFrame, col: str, by_group: str = None) -> pd.Series:
        """截面排名"""
        if by_group:
            return df.groupby(by_group)[col].rank()
        else:
            return df[col].rank()
    
    @staticmethod
    def ewma(df: pd.DataFrame, col: str, span: int, by_group: str = None) -> pd.Series:
        """指数加权移动平均"""
        if by_group:
            return df.groupby(by_group)[col].ewm(span=span).mean().reset_index(0, drop=True)
        else:
            return df[col].ewm(span=span).mean()
