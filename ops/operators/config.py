"""
配置文件
"""

import polars as pl
import os

class CONFIG:
    # 项目根目录
    PROJECT_ROOT = "/home/<USER>/polarsAlpha"

    # 数据目录
    DATA_DIR = os.path.join(PROJECT_ROOT, "disk")
    MAIN_DATA_FILE = os.path.join(DATA_DIR, "OHLCVA_vwap.parquet")

    # 历史配置（保留兼容性）
    PREPROCESS_DIR = "/disk4/shared/intern/laiyc/preprocess/"
    RU_DIR = "/disk4/shared/intern/laiyc/minRawData/ru_min1/"

    # 数据列配置
    DSH_COLS = ['date', 'symbol', 'hhmm']
    OHLCVA_COLS = ['date', 'symbol', 'hhmm', 'Open', 'High', 'Low', 'Close', 'Volume', 'Amount', 'Vwap', 'datetime']

    # 特定时间点
    SPEC_TIME = ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]

    # 向后兼容
    TMP_DIR = DATA_DIR + "/"

def load_data():
    """
    加载主数据文件

    Returns:
        pl.DataFrame: 包含OHLCVA数据的DataFrame
        - 数据规模: 44,220,825行 × 11列
        - 时间范围: 2023-06-01 到 2023-07-31
        - 股票数量: 4,555只
    """
    if not os.path.exists(CONFIG.MAIN_DATA_FILE):
        raise FileNotFoundError(f"数据文件不存在: {CONFIG.MAIN_DATA_FILE}")

    return pl.read_parquet(CONFIG.MAIN_DATA_FILE)

def get_data_info():
    """获取数据基本信息"""
    try:
        df = load_data()
        return {
            "shape": df.shape,
            "columns": df.columns,
            "dtypes": df.dtypes,
            "symbol_count": df["symbol"].n_unique(),
            "date_range": (df["datetime"].min(), df["datetime"].max()),
            "file_path": CONFIG.MAIN_DATA_FILE
        }
    except Exception as e:
        return {"error": str(e)}
