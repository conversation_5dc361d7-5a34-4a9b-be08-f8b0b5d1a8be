import polars as pl
import pandas as pd

valid_factor = pd.read_csv("factor_valid.csv", header=None)[0].to_list()

from pathlib import Path

def get_available_factors():
    """获取所有可用的因子列表"""
    alpha_dir = Path("./alpha299_polars")
    factor_files = list(alpha_dir.glob("factor_*.py"))
    factors = []

    for file_path in factor_files:
        factor_name = file_path.stem  # 去掉.py后缀
        if factor_name.startswith("factor_") and factor_name.split("_")[1].isdigit() and factor_name in valid_factor:
            factor_num = int(factor_name.split("_")[1])
            factors.append((factor_num, factor_name))

    return sorted(factors, key=lambda x: x[0])

get_available_factors()

df = pl.read_parquet("raw_data.parquet")

f