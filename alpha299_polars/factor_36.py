# Alpha299因子 - factor_36 (Polars版本)
# 原始因子编号: 36
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_36(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格排名相关性和价格差值的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 3,          # ts_rank窗口1
        'n2': 12,         # ts_rank窗口2  
        'n3': 18,         # 相关性窗口
        'n4': 4,          # decay_linear窗口1
        'n5': 16,         # ts_rank窗口3
        'n6': 16,         # decay_linear窗口2
        'n7': 4,          # ts_rank窗口4
        'adv_window': 180 # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']
    n6 = window_sizes['n6']
    n7 = window_sizes['n7']
    adv_window = window_sizes['adv_window']

    # 计算VWAP：简化计算，使用Amount/Volume作为近似
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算ADV：按symbol分组，计算移动平均
    adv180 = Ops.rolling_mean("Volume", adv_window)

    # 第一部分计算
    # ts_rank(close, n1)
    close_rank = Ops.rolling_rank("Close", n1)

    # ts_rank(adv180, n2)
    adv180_rank = Ops.rolling_rank(adv180, n2)

    # correlation between close_rank and adv180_rank over n3
    corr = Ops.rolling_corr(close_rank, adv180_rank, n3)

    # decay_linear over n4：线性加权平均
    decay_linear1 = Ops.decaylinear(corr, n4)

    # ts_rank over n5
    part1 = Ops.rolling_rank(decay_linear1, n5)

    # 第二部分计算
    # (low + open) - 2*vwap
    diff = pl.col("Low") + pl.col("Open") - 2 * vwap

    # 使用滚动窗口排名
    rank_diff = Ops.rolling_rank(diff, n5)

    # square
    rank_diff_sq = rank_diff ** 2

    # decay_linear over n6
    decay_linear2 = Ops.decaylinear(rank_diff_sq, n6)

    # ts_rank over n7
    part2 = Ops.rolling_rank(decay_linear2, n7)

    # 取两部分最大值，只在最后使用一次.over()
    factor_result = pl.max_horizontal([part1, part2])

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_36")
