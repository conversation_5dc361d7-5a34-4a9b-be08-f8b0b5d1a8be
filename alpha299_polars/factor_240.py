# Alpha299因子 - factor_240 (Polars版本)
# 原始因子编号: 240
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_240(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha169因子：基于VWAP sigmoid变换、成交量差分最大值与高价百分比变化率最小值的因子
    
    参数:
        w: 可调参数（无实际使用，因子参数固定）
        uni_col: 单列参数（无实际使用，因子使用多列）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算sigmoid(vwap)
    sigmoid_vwap = 1.0 / (1.0 + (-pl.col("Vwap")).exp())

    # 2. 计算volume的2期差值（delta(volume, 2)）
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_delta = volume_protected - volume_protected.shift(2)

    # 3. 取sigmoid_vwap和volume_delta的较大值
    gp_max = pl.max_horizontal([sigmoid_vwap, volume_delta])

    # 4. 计算high的5期百分比变化率（ts_pctchg(high, 5)）
    high_prev5 = pl.col("High").shift(5)
    high_pctchg = (pl.col("High") - high_prev5) / (high_prev5 + eps)

    # 5. 取gp_max和high_pctchg的较小值作为因子值
    factor_result = pl.min_horizontal([gp_max, high_pctchg])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_240")
