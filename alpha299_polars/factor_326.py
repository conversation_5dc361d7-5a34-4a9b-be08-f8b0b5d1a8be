# Alpha299因子 - factor_326 (Polars版本)
# 原始因子编号: 326
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_326(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha107因子：基于volume-high最大值和low的Z-score和与log(vwap)百分比变化的相关系数
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window_zscore': 20,    # 标准化窗口
        'window_corr': 15,      # 相关系数窗口
        'window_pctchg': 8      # 百分比变化窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    window_zscore = window_sizes['window_zscore']
    window_corr = window_sizes['window_corr']
    window_pctchg = window_sizes['window_pctchg']

    # 使用别名避免重复字段问题
    # 1. 计算T1 = gp_max(volume, high)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    T1 = pl.max_horizontal([volume_protected, pl.col("High")]).alias("_T1")

    # 2. 计算Z1 = ts_zscore(low, window_zscore)
    low_mean = Ops.rolling_mean("Low", window_zscore).alias("_low_mean")
    low_std = Ops.rolling_std("Low", window_zscore).alias("_low_std")
    Z1 = ((pl.col("Low") - low_mean) / (low_std + eps)).alias("_Z1")

    # 3. 计算Z2 = ts_zscore(T1, window_zscore)
    T1_mean = Ops.rolling_mean(T1, window_zscore).alias("_T1_mean")
    T1_std = Ops.rolling_std(T1, window_zscore).alias("_T1_std")
    Z2 = ((T1 - T1_mean) / (T1_std + eps)).alias("_Z2")

    # 4. 计算X1 = add(Z1, Z2)
    X1 = (Z1 + Z2).alias("_X1")

    # 5. 计算T2 = log(vwap)
    T2 = (pl.col("Vwap").abs() + eps).log().alias("_T2")

    # 6. 计算X2 = ts_pctchg(T2, window_pctchg)
    T2_shifted = T2.shift(window_pctchg).alias("_T2_shifted")
    X2 = ((T2 - T2_shifted) / (T2_shifted + eps)).alias("_X2")

    # 7. 计算ts_corr(X1, X2, window_corr)
    factor_result = Ops.rolling_corr(X1, X2, window_corr)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_326")
