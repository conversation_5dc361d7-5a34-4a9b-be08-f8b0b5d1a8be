# Alpha299因子 - factor_236 (Polars版本)
# 原始因子编号: 236
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_236(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha164因子：基于low最大值与amount均值协方差的最大值因子
    
    参数:
        w: 基准参数（本因子不使用动态窗口系统）
        uni_col: 单一基础数据列（本因子不适用，保留为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算I1: ts_max(low, 7)
    I1 = Ops.rolling_max("Low", 7)

    # 2. 计算I2: ts_mean(amount, 20)
    I2 = Ops.rolling_mean("Amount", 20)

    # 3. 计算I3: ts_cov(10, I1, I2)
    I3 = Ops.rolling_cov(I1, I2, 10)

    # 4. 计算Alpha164: ts_max(I3, 16)
    factor_result = Ops.rolling_max(I3, 16)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_236")
