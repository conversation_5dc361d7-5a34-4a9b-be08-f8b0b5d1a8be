# Alpha299因子 - factor_332 (Polars版本)
# 原始因子编号: 332
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_332(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha117因子：基于vwap-open差值的双重时序排名对vwap平方根反正切的回归残差
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'w1': 18,  # ts_rank(sub_vwap_open, 18)
        'w2': 9,   # ts_rank(ts_rank_18, 9)
        'w3': 8    # ts_regres(ts_rank_9, arctan_sqrt_vwap, 8)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    w1 = window_sizes['w1']
    w2 = window_sizes['w2']
    w3 = window_sizes['w3']

    # 使用别名避免重复字段问题
    # 1. 计算vwap与open的差值
    sub_vwap_open = (pl.col("Vwap") - pl.col("Open")).alias("_sub_vwap_open")

    # 2. 计算ts_rank(sub_vwap_open, w1)
    ts_rank_18 = (Ops.rolling_rank(sub_vwap_open, w1)).alias("_ts_rank_18")

    # 3. 计算ts_rank(ts_rank_18, w2)
    ts_rank_9 = (Ops.rolling_rank(ts_rank_18, w2)).alias("_ts_rank_9")

    # 4. 计算sqrt(vwap)
    sqrt_vwap = (pl.col("Vwap").abs() + eps).sqrt().alias("_sqrt_vwap")

    # 5. 计算arctan(sqrt_vwap)
    arctan_sqrt_vwap = sqrt_vwap.arctan().alias("_arctan_sqrt_vwap")

    # 6. 计算ts_regres(ts_rank_9, arctan_sqrt_vwap, w3)
    factor_result = Ops.rolling_regres(ts_rank_9, arctan_sqrt_vwap, w3)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_332")
