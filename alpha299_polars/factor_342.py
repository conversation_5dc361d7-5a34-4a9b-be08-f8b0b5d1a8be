# Alpha299因子 - factor_342 (Polars版本)
# 原始因子编号: 342
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_342(w: int | None = 12, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha148因子：基于amount和vwap的Z-score和与open余弦值的回归贝塔和volume-vwap回归贝塔的最大值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window1': 14,  # amount和vwap的Z-score窗口
        'window2': 12,  # T1与T2的回归贝塔窗口
        'window3': 18   # volume与vwap的回归贝塔窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    window1 = window_sizes['window1']
    window2 = window_sizes['window2']
    window3 = window_sizes['window3']

    # 使用别名避免重复字段问题
    # 1. 对amount进行滚动标准化
    amount_mean = Ops.rolling_mean("Amount", window1).alias("_amount_mean")
    amount_std = Ops.rolling_std("Amount", window1).alias("_amount_std")
    amount_zscore = ((pl.col("Amount") - amount_mean) / (amount_std + eps)).alias("_amount_zscore")

    # 2. 对vwap进行滚动标准化
    vwap_mean = Ops.rolling_mean("Vwap", window1).alias("_vwap_mean")
    vwap_std = Ops.rolling_std("Vwap", window1).alias("_vwap_std")
    vwap_zscore = ((pl.col("Vwap") - vwap_mean) / (vwap_std + eps)).alias("_vwap_zscore")

    # 3. 计算标准化后的成交额与标准化后的vwap的和
    T1 = (amount_zscore + vwap_zscore).alias("_T1")

    # 4. 计算开盘价的余弦值（缩放以避免数值问题）
    scaled_open = ((pl.col("Open") + eps) * 0.001).alias("_scaled_open")
    T2 = scaled_open.cos().alias("_T2")

    # 5. 计算T1与T2的回归贝塔系数
    X1 = Ops.rolling_regbeta(T1, T2, window2).alias("_X1")

    # 6. 计算volume与vwap的回归贝塔系数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    X2 = Ops.rolling_regbeta(volume_protected, "Vwap", window3).alias("_X2")

    # 7. 取X1和X2中逐元素的较大值
    factor_result = pl.max_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_342")
