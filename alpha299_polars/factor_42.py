# Alpha299因子 - factor_42 (Polars版本)
# 原始因子编号: 42
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_42(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha92因子：基于价格条件和排名相关性的复合因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1_window': 15,        # 线性衰减窗口
        'n2_window': 19,        # 时间序列排名窗口
        'n3_window': 8,         # 相关性计算窗口
        'n4_window': 7,         # 线性衰减和排名窗口
        'adv_window': 30        # ADV计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 300.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1_window']
    n2 = window_sizes['n2_window']
    n3 = window_sizes['n3_window']
    n4 = window_sizes['n4_window']
    adv_window = window_sizes['adv_window']

    # 计算平均成交量ADV
    adv = Ops.rolling_mean("Volume", adv_window)

    # Part 1: ((HIGH+LOW)/2 + CLOSE) < (LOW+OPEN) 的线性衰减 + 时间序列排名
    part1 = (((pl.col("High") + pl.col("Low")) / 2 + pl.col("Close")) < (pl.col("Low") + pl.col("Open"))).cast(pl.Int32)

    # 线性衰减处理
    part1_decay = Ops.decaylinear(part1, n1)

    # 时间序列排名（归一化到0-1）
    part1_rank = Ops.rolling_rank(part1_decay, n2)

    # Part 2: 低点排名与ADV排名的相关性 + 线性衰减 + 时间序列排名
    # 计算排名（归一化到0-1）
    rank_low = Ops.rolling_rank("Low", n3)
    rank_adv = Ops.rolling_rank(adv, n3)

    # 计算相关性
    corr = Ops.rolling_corr(rank_low, rank_adv, n3)

    # 线性衰减处理
    part2_decay = Ops.decaylinear(corr, n4)

    # 时间序列排名（归一化到0-1）
    part2_rank = Ops.rolling_rank(part2_decay, n4)

    # 取两部分最小值作为最终因子，只在最后使用一次.over()
    factor_result = pl.min_horizontal([part1_rank, part2_rank])

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_42")
