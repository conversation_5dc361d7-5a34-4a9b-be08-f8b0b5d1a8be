# Alpha299因子 - factor_429 (Polars版本)
# 原始因子编号: 429
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def variance_numba(data, window):
    """
    使用Numba优化的方差计算
    """
    n = len(data)
    if n < window:
        return np.full(n, np.nan)
    
    variance = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # 获取窗口数据
        start_idx = i - window + 1
        window_data = data[start_idx:i+1]
        
        # 计算均值
        mean_value = np.mean(window_data)
        
        # 计算平方的均值
        mean_square = np.mean(window_data ** 2)
        
        # 计算方差: VAR = E[X^2] - (E[X])^2
        var_value = mean_square - (mean_value ** 2)
        
        # 处理可能由于浮点数精度问题为负数的情况
        variance[i] = max(0.0, var_value)
    
    return variance

def factor_429(w: int | None = 5, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算方差(Variance, VAR)因子
    
    参数:
        w: 回看周期，默认为5
        uni_col: 用于计算方差的列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'variance_window': 5
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    variance_window = window_sizes['variance_window']
    
    def apply_variance(data_values):
        return variance_numba(data_values, variance_window)
    
    return pl.map_batches(
        exprs=[pl.col(uni_col)],
        function=lambda x: pl.Series(apply_variance(x[0].to_numpy()))
    ).over("symbol").alias("factor_429")
