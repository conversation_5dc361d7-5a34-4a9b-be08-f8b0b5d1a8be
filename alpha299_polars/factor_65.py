# Alpha299因子 - factor_65 (Polars版本)
# 原始因子编号: 65
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_65(w: int | None = 6, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于收盘价标准差排名的因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（默认为'Close'）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 6

    # 计算收盘价的标准差
    std_close = Ops.rolling_std(uni_col, window)

    # 对标准差进行排名
    rank_std = Ops.rolling_rank(std_close, window)

    # 只在最后使用一次.over()
    return rank_std.over("symbol").cast(pl.Float32).alias("factor_65")
