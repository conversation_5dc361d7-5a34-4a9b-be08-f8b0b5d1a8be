# Alpha299因子 - factor_298 (Polars版本)
# 原始因子编号: 298
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_298(w: int | None = 2, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha55因子：基于复杂的Z-score、排名、回归残差等计算（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delay_amount_window': 2,    # delay(amount, 2)
        'delay_vwap_window': 5,      # delay(vwap, 5)
        'volume_zscore_window': 20,  # ts_zscore(volume, 20)
        'rank_zscore_window': 10,    # ts_zscore(volume_rank, 10)
        'close_zscore_window': 10,   # ts_zscore(close, 10)
        'regres_window': 7,          # ts_regres(delay_vwap_5, add_result, 7)
        'log_zscore_window': 10,     # ts_zscore(log_delay_amount, 10)
        'regres_zscore_window': 10,  # ts_zscore(vwap_add_regres, 10)
        'pctchg_window': 13          # ts_pctchg(min_result, 13)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delay_amount_window = window_sizes['delay_amount_window']
    delay_vwap_window = window_sizes['delay_vwap_window']
    volume_zscore_window = window_sizes['volume_zscore_window']
    rank_zscore_window = window_sizes['rank_zscore_window']
    close_zscore_window = window_sizes['close_zscore_window']
    regres_window = window_sizes['regres_window']
    log_zscore_window = window_sizes['log_zscore_window']
    regres_zscore_window = window_sizes['regres_zscore_window']
    pctchg_window = window_sizes['pctchg_window']

    # 1. delay(amount, 2) -> log(delay_amount_2)
    delay_amount = pl.col("Amount").shift(delay_amount_window)
    log_delay_amount = (delay_amount.abs() + eps).log()

    # 2. delay(vwap, 5)
    delay_vwap = pl.col("Vwap").shift(delay_vwap_window)

    # 3. ts_zscore(volume, 20)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, volume_zscore_window)
    volume_std = Ops.rolling_std(volume_protected, volume_zscore_window)
    volume_zscore = (volume_protected - volume_mean) / (volume_std + eps)

    # 4. 简化实现：使用时序排名代替横截面排名
    volume_rank = Ops.rolling_rank(volume_zscore, rank_zscore_window)

    # 5. ts_zscore(volume_rank, 10)
    rank_mean = Ops.rolling_mean(volume_rank, rank_zscore_window)
    rank_std = Ops.rolling_std(volume_rank, rank_zscore_window)
    volume_rank_zscore = (volume_rank - rank_mean) / (rank_std + eps)

    # 6. ts_zscore(close, 10)
    close_mean = Ops.rolling_mean("Close", close_zscore_window)
    close_std = Ops.rolling_std("Close", close_zscore_window)
    close_zscore = (pl.col("Close") - close_mean) / (close_std + eps)

    # 7. add(volume_rank_zscore, close_zscore)
    add_result = volume_rank_zscore + close_zscore

    # 8. ts_regres(delay_vwap_5, add_result, 7)
    vwap_add_regres = Ops.rolling_regres(delay_vwap, add_result, regres_window)

    # 9. ts_zscore(log_delay_amount, 10)
    log_mean = Ops.rolling_mean(log_delay_amount, log_zscore_window)
    log_std = Ops.rolling_std(log_delay_amount, log_zscore_window)
    log_amount_zscore = (log_delay_amount - log_mean) / (log_std + eps)

    # 10. ts_zscore(vwap_add_regres, 10)
    regres_mean = Ops.rolling_mean(vwap_add_regres, regres_zscore_window)
    regres_std = Ops.rolling_std(vwap_add_regres, regres_zscore_window)
    regres_zscore = (vwap_add_regres - regres_mean) / (regres_std + eps)

    # 11. gp_min(log_amount_zscore, regres_zscore)
    min_result = pl.min_horizontal([log_amount_zscore, regres_zscore])

    # 12. ts_pctchg(min_result, 13)
    min_shifted = min_result.shift(pctchg_window)
    factor_result = (min_result - min_shifted) / (min_shifted + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_298")
