# Alpha299因子 - factor_130 (Polars版本 - 准确实现)
# 原始因子编号: 130
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_130(w: int | None = 4, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及多个相关系数、Z-score标准化、截面排名

    参数:
        w: 基础窗口大小，默认4
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = w          # VWAP和Volume相关性窗口
    n2 = 5 * w      # 标准化窗口
    n3 = 12 * w     # 成交量移动平均窗口
    n4 = 3 * w      # 最终相关性窗口

    # 1. 计算CorrVWAPVol_t：VWAP和Volume的n1期滚动相关系数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    CorrVWAPVol_t = Ops.rolling_corr(pl.col("Vwap"), volume_protected, n1).over("symbol").alias("_CorrVWAPVol_t")

    # 2. 对CorrVWAPVol_t进行横截面百分比排序
    R1 = ((pl.col("_CorrVWAPVol_t").rank(method="average") / pl.len()).over("datetime")).alias("_R1")

    # 3. 计算MA_n3(Volume)和对其进行n2期滚动标准化
    MA_n3_Volume = Ops.rolling_mean(volume_protected, n3)
    mav_mean = Ops.rolling_mean(MA_n3_Volume, n2)
    mav_std = Ops.rolling_std(MA_n3_Volume, n2)
    ZScoreMAVol50_t = ((MA_n3_Volume - mav_mean) / (mav_std + eps)).over("symbol").alias("_ZScoreMAVol50_t")

    # 4. 对Low进行n2期滚动标准化
    low_mean = Ops.rolling_mean("Low", n2)
    low_std = Ops.rolling_std("Low", n2)
    ZScoreLow_t = ((pl.col("Low") - low_mean) / (low_std + eps)).over("symbol").alias("_ZScoreLow_t")

    # 5. 对ZScoreLow_t和ZScoreMAVol50_t进行横截面百分比排序
    RankZScoreLow_t = ((pl.col("_ZScoreLow_t").rank(method="average") / pl.len()).over("datetime")).alias("_RankZScoreLow_t")
    RankZScoreMAVol50_t = ((pl.col("_ZScoreMAVol50_t").rank(method="average") / pl.len()).over("datetime")).alias("_RankZScoreMAVol50_t")

    # 6. 计算RankZScoreLow_t和RankZScoreMAVol50_t的n4期滚动相关系数
    CorrZScoreLowMAVol_t = Ops.rolling_corr(pl.col("_RankZScoreLow_t"), pl.col("_RankZScoreMAVol50_t"), n4).over("symbol").alias("_CorrZScoreLowMAVol_t")

    # 7. 对CorrZScoreLowMAVol_t进行横截面百分比排序
    R2 = pl.col("_CorrZScoreLowMAVol_t").rank(method="average").over("datetime").alias("_R2")

    # 8. 计算最终因子值
    factor_result = (pl.col("_R1") * pl.col("_R2")).over("symbol").cast(pl.Float32).alias("factor_130")

    return [CorrVWAPVol_t, R1, ZScoreMAVol50_t, ZScoreLow_t, RankZScoreLow_t, RankZScoreMAVol50_t, CorrZScoreLowMAVol_t, R2, factor_result]
