# Alpha299因子 - factor_135 (Polars版本)
# 原始因子编号: 135
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_135(w: int | None = 2, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha25因子：基于价格动量、成交量相对强度和长期收益表现的组合（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
        uni_col: 用于计算的核心基础数据列，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_window': 2,         # 价格动量差分窗口
        'zscore_window': 2,        # Z-score标准化窗口
        'volume_ma_window': 3,     # 成交量移动平均窗口
        'decay_window': 2,         # 线性衰减窗口
        'ret_sum_window': 20       # 收益率累计窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_window = window_sizes['delta_window']
    zscore_window = window_sizes['zscore_window']
    volume_ma_window = window_sizes['volume_ma_window']
    decay_window = window_sizes['decay_window']
    ret_sum_window = window_sizes['ret_sum_window']

    # 1. 计算R1: 价格动量Z-score排名
    delta_close = pl.col(uni_col) - pl.col(uni_col).shift(delta_window)
    
    # 计算滚动标准化
    delta_mean = Ops.rolling_mean(delta_close, zscore_window)
    delta_std = Ops.rolling_std(delta_close, zscore_window)
    zscore_delta = (delta_close - delta_mean) / (delta_std + eps)
    
    # 简化实现：使用时序排名代替横截面排名
    R1 = Ops.rolling_rank(zscore_delta, zscore_window)

    # 2. 计算R2: 成交量相对强度衰减排名
    volume_ma = Ops.rolling_mean("Volume", volume_ma_window)
    V_t = pl.col("Volume") / (volume_ma + eps)
    
    # 计算线性衰减
    decay_V = Ops.decaylinear(V_t, decay_window)
    
    # 简化实现：使用时序排名代替横截面排名
    rank_decay_V = Ops.rolling_rank(decay_V, decay_window)
    R2 = 1 - rank_decay_V

    # 3. 计算R3: 长期收益表现排名
    ret_t = (pl.col(uni_col) - pl.col(uni_col).shift(1)) / (pl.col(uni_col).shift(1) + eps)
    ret_sum = Ops.rolling_sum(ret_t, ret_sum_window)
    
    # 简化实现：使用时序排名代替横截面排名
    rank_ret_sum = Ops.rolling_rank(ret_sum, ret_sum_window)
    R3 = 1 + rank_ret_sum

    # 4. 计算最终因子值
    factor_result = -(R1 * R2 * R3)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_135")
