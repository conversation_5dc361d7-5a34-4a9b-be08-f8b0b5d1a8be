# Alpha299因子 - factor_406 (Polars版本)
# 原始因子编号: 406
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def sarext_numba(high, low, 
                 start_value=0.0,
                 offset_on_reverse=0.0,
                 acceleration_init_long=0.02,
                 acceleration_long=0.02,
                 acceleration_max_long=0.20,
                 acceleration_init_short=0.02,
                 acceleration_short=0.02,
                 acceleration_max_short=0.20):
    """
    使用Numba优化的SAREXT计算
    """
    n = len(high)
    if n < 2:
        return np.full(n, np.nan)
    
    sarext = np.full(n, np.nan)
    
    # 参数一致性调整
    acceleration_init_long = max(0.0, acceleration_init_long)
    acceleration_long = max(0.0, acceleration_long)
    acceleration_max_long = max(acceleration_init_long, acceleration_max_long)
    acceleration_max_long = max(acceleration_long, acceleration_max_long)
    
    acceleration_init_short = max(0.0, acceleration_init_short)
    acceleration_short = max(0.0, acceleration_short)
    acceleration_max_short = max(acceleration_init_short, acceleration_max_short)
    acceleration_max_short = max(acceleration_short, acceleration_max_short)
    
    offset_on_reverse = max(0.0, offset_on_reverse)
    
    # 初始化变量
    init_trend = 0  # 1表示上涨，-1表示下跌
    sar_initial = 0.0
    
    # 确定初始趋势和SAR值
    if start_value > 0:
        # 指定初始为上涨趋势
        init_trend = 1
        sar_initial = start_value
        ep = high[0]  # 第一个有效周期的最高价
        af = acceleration_init_long
    elif start_value < 0:
        # 指定初始为下跌趋势
        init_trend = -1
        sar_initial = abs(start_value)
        ep = low[0]  # 第一个有效周期的最低价
        af = acceleration_init_short
    else:
        # 自动检测初始趋势
        up_move = high[1] - high[0]
        down_move = low[0] - low[1]
        
        plus_dm = max(0, up_move) if up_move > down_move and up_move > 0 else 0
        minus_dm = max(0, down_move) if down_move > up_move and down_move > 0 else 0
        
        if minus_dm > plus_dm:
            init_trend = -1
            sar_initial = high[0]  # 前一周期最高价
            ep = low[1]
            af = acceleration_init_short
        else:
            init_trend = 1
            sar_initial = low[0]  # 前一周期最低价
            ep = high[1]
            af = acceleration_init_long
    
    # Set the first output value
    if init_trend == 1:
        sarext[1] = sar_initial
    else:
        sarext[1] = -sar_initial
    
    # For next period calculation of SAR
    if init_trend == 1:
        sar_next = sar_initial + af * (ep - sar_initial)
        sar_next = min(sar_next, low[0], low[1])
    else:
        sar_next = sar_initial + af * (ep - sar_initial)
        sar_next = max(sar_next, high[0], high[1])
    
    # Initialize current state
    sar_current = sar_next
    ep_prev = ep
    af_prev = af
    trend_prev = init_trend
    
    # Iterate to calculate the remaining SAREXT values
    for i in range(2, n):
        # Check for trend reversal
        if trend_prev == 1:  # Current is uptrend
            if low[i] <= sar_current:  # Trend reverses to downtrend
                trend_current = -1
                sar_current = ep_prev
                sar_current = max(sar_current, high[i-1], high[i])  # Boundary limit
                sar_current = sar_current * (1 + offset_on_reverse)  # Apply reversal offset
                sarext[i] = -sar_current
                af_current = acceleration_init_short  # Reset AF
                ep_current = low[i]  # New extreme price
            else:  # Continue uptrend
                trend_current = 1
                sarext[i] = sar_current  # Output value is current SAR
                if high[i] > ep_prev:  # Made a new high
                    ep_current = high[i]
                    af_current = min(af_prev + acceleration_long, acceleration_max_long)
                else:  # Did not make a new high
                    ep_current = ep_prev
                    af_current = af_prev
        else:  # Current is downtrend
            if high[i] >= sar_current:  # Trend reverses to uptrend
                trend_current = 1
                sar_current = ep_prev
                sar_current = min(sar_current, low[i-1], low[i])  # Boundary limit
                sar_current = sar_current * (1 - offset_on_reverse)  # Apply reversal offset
                sarext[i] = sar_current
                af_current = acceleration_init_long  # Reset AF
                ep_current = high[i]  # New extreme price
            else:  # Continue downtrend
                trend_current = -1
                sarext[i] = -sar_current  # Output value is negative of current SAR
                if low[i] < ep_prev:  # Made a new low
                    ep_current = low[i]
                    af_current = min(af_prev + acceleration_short, acceleration_max_short)
                else:  # Did not make a new low
                    ep_current = ep_prev
                    af_current = af_prev
        
        # Calculate the next period's SAR value
        sar_next = sar_current + af_current * (ep_current - sar_current)
        
        # Apply boundary limits
        if trend_current == 1:  # Uptrend
            sar_next = min(sar_next, low[i-1], low[i])
        else:  # Downtrend
            sar_next = max(sar_next, high[i-1], high[i])
        
        # Update state variables
        sar_current = sar_next
        ep_prev = ep_current
        af_prev = af_current
        trend_prev = trend_current
    
    return sarext

def factor_406(w: int | None = None, uni_col: str | None = None, 
               start_value: float = 0.0,
               offset_on_reverse: float = 0.0,
               acceleration_init_long: float = 0.02,
               acceleration_long: float = 0.02,
               acceleration_max_long: float = 0.20,
               acceleration_init_short: float = 0.02,
               acceleration_short: float = 0.02,
               acceleration_max_short: float = 0.20) -> pl.Expr:
    """
    计算抛物线转向指标扩展版 (Parabolic Stop and Reverse Extended, SAREXT)
    
    参数:
        w: 无时间窗口参数，设为None
        uni_col: 无单一列参数，设为None
        start_value: 初始SAR值和方向，0表示自动检测
        offset_on_reverse: 趋势反转时的调整百分比
        acceleration_init_long: 上涨趋势的初始加速因子
        acceleration_long: 上涨趋势的加速因子步长
        acceleration_max_long: 上涨趋势的最大加速因子
        acceleration_init_short: 下跌趋势的初始加速因子
        acceleration_short: 下跌趋势的加速因子步长
        acceleration_max_short: 下跌趋势的最大加速因子
        
    返回:
        pl.Expr: 因子表达式
    """
    def apply_sarext(high_values, low_values):
        return sarext_numba(
            high_values, low_values,
            start_value, offset_on_reverse,
            acceleration_init_long, acceleration_long, acceleration_max_long,
            acceleration_init_short, acceleration_short, acceleration_max_short
        )

    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low")],
        function=lambda x: pl.Series(apply_sarext(x[0].to_numpy(), x[1].to_numpy()))
    ).over("symbol").alias("factor_406")
