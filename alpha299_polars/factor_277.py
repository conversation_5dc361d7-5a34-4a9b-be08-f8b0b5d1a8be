# Alpha299因子 - factor_277 (Polars版本)
# 原始因子编号: 277
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_277(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha27因子：基于volume和high的Z-score和与amount反正切乘积的对数绝对值，与close/open比值标准差减去low的Z-score的回归贝塔
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'beta_window': 6,        # ts_regbeta的窗口
        'zscore_window': 14      # ts_zscore和ts_std的窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    beta_window = window_sizes['beta_window']
    zscore_window = window_sizes['zscore_window']

    # 步骤1-2: 对volume和high进行滚动Z-score
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, zscore_window)
    volume_std = Ops.rolling_std(volume_protected, zscore_window)
    T1a = (volume_protected - volume_mean) / (volume_std + eps)

    high_mean = Ops.rolling_mean("High", zscore_window)
    high_std = Ops.rolling_std("High", zscore_window)
    T1b = (pl.col("High") - high_mean) / (high_std + eps)

    # 步骤3: 计算T1a + T1b
    T1 = T1a + T1b

    # 步骤4: 计算amount的反正切
    T2 = pl.col("Amount").arctan()

    # 步骤5: T1 * T2
    T3 = T1 * T2

    # 步骤6: 取绝对值的自然对数
    X1 = (T3.abs() + eps).log()

    # 步骤7: 计算close/open的ratio
    T4 = pl.col("Close") / (pl.col("Open") + eps)

    # 步骤8: 计算T4的滚动标准差
    T5 = Ops.rolling_std(T4, zscore_window)

    # 步骤9: 对low进行滚动Z-score
    low_mean = Ops.rolling_mean("Low", zscore_window)
    low_std = Ops.rolling_std("Low", zscore_window)
    T5a = (pl.col("Low") - low_mean) / (low_std + eps)

    # 步骤10: T5 - T5a
    X2 = T5 - T5a

    # 步骤11: 计算X2对X1的滚动回归Beta
    factor_result = Ops.rolling_regbeta(X2, X1, beta_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_277")
