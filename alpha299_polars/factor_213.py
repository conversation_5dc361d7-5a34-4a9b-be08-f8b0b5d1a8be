# Alpha299因子 - factor_213 (Polars版本)
# 原始因子编号: 213
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_213(w: int | None = 8, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha127因子：基于VWAP、amount和close的复合技术分析因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_min': 15,        # 滚动最小值窗口
        'n_delta': 8,       # delta差分窗口
        'n_zscore': 20      # Z-score计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_min = window_sizes['n_min']
    n_delta = window_sizes['n_delta']
    n_zscore = window_sizes['n_zscore']

    # 1. 取vwap相反数
    T1 = -pl.col("Vwap")

    # 2. 滚动最小值
    T2 = Ops.rolling_min(T1, n_min)

    # 3. amount绝对值
    T3 = pl.col("Amount").abs()

    # 4-5. 滚动Z-score
    T2_mean = Ops.rolling_mean(T2, n_zscore)
    T2_std = Ops.rolling_std(T2, n_zscore)
    Z2 = (T2 - T2_mean) / (T2_std + eps)

    T3_mean = Ops.rolling_mean(T3, n_zscore)
    T3_std = Ops.rolling_std(T3, n_zscore)
    Z3 = (T3 - T3_mean) / (T3_std + eps)

    # 6. Z2-Z3
    T4 = Z2 - Z3

    # 7. close/T4
    X1 = pl.col(uni_col) / (T4 + eps)

    # 8. open_price的delta
    X2 = pl.col("Open") - pl.col("Open").shift(n_delta)

    # 9. 取较小值
    factor_result = pl.min_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_213")
