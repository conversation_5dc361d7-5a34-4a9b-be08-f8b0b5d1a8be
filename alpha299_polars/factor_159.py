# Alpha299因子 - factor_159 (Polars版本)
# 原始因子编号: 159
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_159(w: int | None = 9, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha57因子：平滑随机震荡%K因子
    
    参数:
        w: 滚动窗口期天数，默认为9天
        uni_col: 单一基础列，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'w': 9  # 基础窗口参数
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    actual_w = min(window_sizes['w'], 30)  # 限制最大窗口为30

    # 1. 计算滚动最低价L9和最高价H9
    L9 = Ops.rolling_min("Low", actual_w)
    H9 = Ops.rolling_max("High", actual_w)

    # 2. 计算随机震荡%K
    K_percent = ((pl.col(uni_col) - L9) / (H9 - L9 + eps)) * 100

    # 3. 计算EMA(span=5)
    alpha_ema = 2.0 / (5 + 1)
    factor_result = K_percent.ewm_mean(alpha=alpha_ema, adjust=False)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_159")
