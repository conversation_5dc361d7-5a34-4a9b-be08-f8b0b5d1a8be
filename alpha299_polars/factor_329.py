# Alpha299因子 - factor_329 (Polars版本)
# 原始因子编号: 329
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_329(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha110因子：基于volume平方根与open最小值和volume最大值相关系数的负数加上low-volume最小值差分的Z-score
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'corr_window': 18,      # 相关系数窗口
        'max_window': 6,        # 最大值窗口
        'delta_window': 6,      # 差分窗口
        'zscore_window': 20     # Z-score窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    corr_window = window_sizes['corr_window']
    max_window = window_sizes['max_window']
    delta_window = window_sizes['delta_window']
    zscore_window = window_sizes['zscore_window']

    # 使用别名避免重复字段问题
    # 1. 计算sqrt(volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    sqrt_volume = (volume_protected.abs() + eps).sqrt().alias("_sqrt_volume")

    # 2. 计算gp_min(sqrt_volume, open)
    min_sqrt_volume_open = pl.min_horizontal([sqrt_volume, pl.col("Open")]).alias("_min_sqrt_volume_open")

    # 3. 计算volume的滚动最大值
    volume_max = Ops.rolling_max(volume_protected, max_window).alias("_volume_max")

    # 4. 计算ts_corr(min_sqrt_volume_open, volume_max, corr_window)
    corr_result = Ops.rolling_corr(min_sqrt_volume_open, volume_max, corr_window).alias("_corr_result")

    # 5. 计算neg_corr = -corr_result
    neg_corr = (-corr_result).alias("_neg_corr")

    # 6. 计算gp_min(low, volume)
    min_low_volume = pl.min_horizontal([pl.col("Low"), volume_protected]).alias("_min_low_volume")

    # 7. 计算delta(min_low_volume, delta_window)
    delta_min_low_volume = (min_low_volume - min_low_volume.shift(delta_window)).alias("_delta_min_low_volume")

    # 8. 计算ts_zscore(delta_min_low_volume, zscore_window)
    delta_mean = Ops.rolling_mean(delta_min_low_volume, zscore_window).alias("_delta_mean")
    delta_std = Ops.rolling_std(delta_min_low_volume, zscore_window).alias("_delta_std")
    zscore_delta_min_low_volume = ((delta_min_low_volume - delta_mean) / (delta_std + eps)).alias("_zscore_delta_min_low_volume")

    # 9. 计算neg_corr + zscore_delta_min_low_volume
    factor_result = neg_corr + zscore_delta_min_low_volume

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_329")
