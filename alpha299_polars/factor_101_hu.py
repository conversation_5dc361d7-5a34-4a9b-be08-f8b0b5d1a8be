# Alpha299因子 - factor_101 (Polars版本)
# 原始因子编号: 101
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_101(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格移动和波动幅度的复合因子
    
    参数:
        w: 基准参数（本因子不使用窗口，设为None）
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算前一日的价格
    C_t_minus_1 = pl.col("Close").shift(1)
    O_t_minus_1 = pl.col("Open").shift(1)
    L_t_minus_1 = pl.col("Low").shift(1)

    # 计算分子项
    PriceMove_t = (
        pl.col("Close") - C_t_minus_1 +
        (pl.col("Close") - pl.col("Open")) / (2 + eps) +
        C_t_minus_1 - O_t_minus_1
    )

    VolAmp_t = pl.max_horizontal([
        (pl.col("High") - C_t_minus_1).abs(),
        (pl.col("Low") - C_t_minus_1).abs()
    ])

    N_t = 16 * PriceMove_t * VolAmp_t

    # 计算分母项
    TermA = (pl.col("High") - C_t_minus_1).abs()
    TermB = (pl.col("Low") - C_t_minus_1).abs()
    TermC = (pl.col("High") - L_t_minus_1).abs()
    TermD = (C_t_minus_1 - O_t_minus_1).abs()

    # 创建条件判断
    cond1 = (TermA > TermB) & (TermA > TermC)
    cond2 = (TermB > TermC) & (TermB > TermA)

    # 计算分母D_t
    D_t = pl.when(cond1).then(
        TermA + TermB / (2 + eps) + TermD / (4 + eps)
    ).when(cond2).then(
        TermB + TermA / (2 + eps) + TermD / (4 + eps)
    ).otherwise(
        TermC + TermD / (4 + eps)
    )

    # 计算最终因子值
    factor_result = N_t / (D_t + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_101")
