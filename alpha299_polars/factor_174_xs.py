# Alpha299因子 - factor_174 (Polars版本 - 准确实现)
# 原始因子编号: 174
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_174(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及中间价、VWAP、线性衰减、Z-score、截面排名、滚动相关系数

    参数:
        w: 基础窗口大小，默认20
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    linear_decay_20 = w      # 第一次线性衰减窗口
    zscore_20 = w           # Z-score窗口
    ma_40 = 2 * w           # 移动平均窗口
    corr_3 = max(1, w // 7) # 相关系数窗口
    linear_decay_6 = max(1, w // 3)  # 第二次线性衰减窗口

    # 1. 计算中间价 Mid_t和Term1_t = Mid_t - VWAP
    Mid_t = ((pl.col("High") + pl.col("Low")) / 2).alias("_Mid_t")
    Term1_t = (pl.col("_Mid_t") - pl.col("Vwap")).alias("_Term1_t")

    # 2. 线性衰减加权 DecayLinear(Term1_t)
    DL_1 = Ops.decaylinear(pl.col("_Term1_t"), linear_decay_20).over("symbol").alias("_DL_1")

    # 3. 滚动标准化 ts_zscore(DL_1)
    dl1_mean = Ops.rolling_mean(pl.col("_DL_1"), zscore_20)
    dl1_std = Ops.rolling_std(pl.col("_DL_1"), zscore_20)
    Z_DL_1 = ((pl.col("_DL_1") - dl1_mean) / (dl1_std + eps)).over("symbol").alias("_Z_DL_1")

    # 4. 横截面排序 R_1 = rank_cs(Z_DL_1)
    R_1 = ((pl.col("_Z_DL_1").rank(method="average") / pl.len()).over("datetime")).alias("_R_1")

    # 5. 计算MA_40(Volume)和滚动相关系数 Corr_2
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    MA_40_Volume = Ops.rolling_mean(volume_protected, ma_40)
    Corr_2 = Ops.rolling_corr(pl.col("_Mid_t"), MA_40_Volume, corr_3).over("symbol").alias("_Corr_2")

    # 6. 线性衰减加权 DecayLinear(Corr_2)
    DL_2 = Ops.decaylinear(pl.col("_Corr_2"), linear_decay_6).over("symbol").alias("_DL_2")

    # 7. 横截面排序 R_2 = rank_cs(DL_2)
    R_2 = ((pl.col("_DL_2").rank(method="average") / pl.len()).over("datetime")).alias("_R_2")

    # 8. 最终因子值 alpha_77 = min(R_1, R_2)
    factor_result = pl.min_horizontal([pl.col("_R_1"), pl.col("_R_2")]).cast(pl.Float32).alias("factor_174")

    return [Mid_t, Term1_t, DL_1, Z_DL_1, R_1, Corr_2, DL_2, R_2, factor_result]
