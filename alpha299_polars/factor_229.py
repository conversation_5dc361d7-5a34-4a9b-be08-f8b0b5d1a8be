# Alpha299因子 - factor_229 (Polars版本)
# 原始因子编号: 229
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_229(w: int | None = 12, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha151因子：基于arctan(high)与open百分比变化和volume差分最大值的相关性
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为12天。
        uni_col: 不适用（因子涉及多列计算）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'pctchg_window': 14,  # ts_pctchg窗口
        'delta_window': 12,   # delta窗口
        'corr_window': 12     # ts_corr窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['pctchg_window']  # ts_pctchg窗口
    n2 = window_sizes['delta_window']   # delta窗口
    n3 = window_sizes['corr_window']    # ts_corr窗口

    # 步骤1: arctan(high)
    X1 = (pl.col("High") + eps).arctan()

    # 步骤2: ts_pctchg(open, n1)
    open_shifted = pl.col("Open").shift(n1)
    T1 = (pl.col("Open") - open_shifted) / (open_shifted + eps)

    # 步骤3: delta(volume, n2)
    T2 = pl.col("Volume") - pl.col("Volume").shift(n2)

    # 步骤4: max(T1, T2)
    X2 = pl.max_horizontal([T1, T2])

    # 步骤5: ts_corr(X1, X2, n3)
    factor_result = Ops.rolling_corr(X1, X2, n3)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_229")
