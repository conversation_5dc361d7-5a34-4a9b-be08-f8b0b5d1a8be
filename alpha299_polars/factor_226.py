# Alpha299因子 - factor_226 (Polars版本)
# 原始因子编号: 226
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_226(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha147因子：基于Z-score和价格变化的组合，包含sigmoid变换和对数价格变化率
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础数据列（本因子不适用，保留为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,  # ts_zscore窗口
        'pctchg_window': 9    # ts_pctchg窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_zscore = window_sizes['zscore_window']  # ts_zscore窗口
    n_pctchg = window_sizes['pctchg_window']  # ts_pctchg窗口

    # 1. 计算low的滚动Z-score
    low_mean = Ops.rolling_mean("Low", n_zscore)
    low_std = Ops.rolling_std("Low", n_zscore)
    low_zscore = (pl.col("Low") - low_mean) / (low_std + eps)

    # 2. 计算volume的滚动Z-score
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, n_zscore)
    volume_std = Ops.rolling_std(volume_protected, n_zscore)
    volume_zscore = (volume_protected - volume_mean) / (volume_std + eps)

    # 3. 计算T1 = low_zscore - volume_zscore
    T1 = low_zscore - volume_zscore

    # 4. 计算sigmoid(T1)
    X1 = 1.0 / (1.0 + (-T1).exp())

    # 5. 计算log(close)
    log_close = (pl.col("Close").abs() + eps).log()

    # 6. 计算log_close的n_pctchg周期百分比变化
    log_close_shifted = log_close.shift(n_pctchg)
    X2 = (log_close - log_close_shifted) / (log_close_shifted + eps)

    # 7. 计算Alpha147 = X1 - X2
    factor_result = X1 - X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_226")
