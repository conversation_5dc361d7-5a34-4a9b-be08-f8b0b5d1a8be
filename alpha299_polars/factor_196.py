# Alpha299因子 - factor_196 (Polars版本)
# 原始因子编号: 196
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_196(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha102因子：基于成交金额最大值、VWAP相关性和价格差分的最小值因子
    
    参数:
        w: 基础窗口参数，用于推导其他窗口参数，默认5
        uni_col: 用于指定单一基础数据列（本因子不使用）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式       



        
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 8,    # w，ts_max窗口
        'n2': 16,   # 2*w，ts_corr窗口
        'n3': 5     # int(5*w/8) = int(5*8/8) = 5，delta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']


    # 3. 计算T1 = ts_max(amount, n1)
    T1 = Ops.rolling_max("Amount", n1)

    # 4. 计算T2 = sqrt(|T1|)
    T2 = T1.abs().sqrt()

    # 5. 计算X1 = ts_corr(vwap, T2, n2)
    X1 = Ops.rolling_corr("Vwap", T2, n2)

    # 6. 计算X2 = delta(close, n3)
    X2 = pl.col("Close") - pl.col("Close").shift(n3)

    # 7. 计算Alpha102 = min(X1, X2)
    factor_result = pl.min_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_196")
