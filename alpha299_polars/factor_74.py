# Alpha299因子 - factor_74 (Polars版本)
# 原始因子编号: 74
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_74(w: int | None = 9, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于收盘价与成交量相关性的因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（默认为'Close'）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 9

    # 计算收盘价与成交量的相关性
    corr = Ops.rolling_corr(uni_col, "Volume", window)

    # 对相关性进行排名并取负
    rank_corr = Ops.rolling_rank(corr, window)
    factor_result = -rank_corr

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_74")
