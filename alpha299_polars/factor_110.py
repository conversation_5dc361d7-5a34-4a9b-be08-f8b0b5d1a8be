# Alpha299因子 - factor_110 (Polars版本)
# 原始因子编号: 110
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_110(w: int | None = 9, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 152因子：复杂的价格相对强度EMA因子
    
    原始逻辑：
    1. 计算价格相对强度 RelStr_t = Close_t / Close_{t-relstr_lag}
    2. 取其1期滞后值 LagRS_t = RelStr_{t-1}
    3. 对 LagRS_t 进行EMA平滑得到 EMA1_LRS_t
    4. 取 EMA1_LRS_t 的1期滞后值 LagEMA1_LRS_t
    5. 计算 LagEMA1_LRS_t 的MA12和MA26
    6. 计算 MA12 和 MA26 的差值 DiffMA_t
    7. 对 DiffMA_t 进行EMA平滑得到原始因子值
    8. 对原始因子值进行 tanh 变换
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为9天
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_110"
    """
    
    # 定义所有窗口的基准值
    window_configs = {
        'relstr_lag': 9,        # 价格相对强度滞后期
        'ema_span': 17,         # EMA的span参数
        'ma12_window': 12,      # MA12窗口
        'ma26_window': 26       # MA26窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三段式混合模型：动态窗口计算函数
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1
        alpha = 1.0
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 根据w的值选择不同的计算模式
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (v / max_base))) 
                    for k, v in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:  # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    relstr_lag = window_sizes['relstr_lag']
    ema_span = window_sizes['ema_span']
    ma12_window = window_sizes['ma12_window']
    ma26_window = window_sizes['ma26_window']
    
    # 步骤1: 计算价格相对强度 RelStr_t = Close_t / Close_{t-relstr_lag}
    close_lag = pl.col(uni_col).shift(relstr_lag)
    rel_str = pl.col(uni_col) / (close_lag + eps)
    
    # 步骤2: 取其1期滞后值 LagRS_t = RelStr_{t-1}
    lag_rs = rel_str.shift(1)
    
    # 步骤3: 对 LagRS_t 进行EMA平滑得到 EMA1_LRS_t
    alpha_ema = 2.0 / (ema_span + 1)
    ema1_lrs = lag_rs.ewm_mean(alpha=alpha_ema, adjust=False)
    
    # 步骤4: 取 EMA1_LRS_t 的1期滞后值 LagEMA1_LRS_t
    lag_ema1_lrs = ema1_lrs.shift(1)
    
    # 步骤5: 计算 LagEMA1_LRS_t 的MA12和MA26
    ma12 = Ops.rolling_mean(lag_ema1_lrs, ma12_window)
    ma26 = Ops.rolling_mean(lag_ema1_lrs, ma26_window)
    
    # 步骤6: 计算 MA12 和 MA26 的差值 DiffMA_t
    diff_ma = ma12 - ma26
    
    # 步骤7: 对 DiffMA_t 进行EMA平滑得到原始因子值
    alpha152_raw = diff_ma.ewm_mean(alpha=alpha_ema, adjust=False)
    
    # 步骤8: 对原始因子值进行 tanh 变换
    factor_value = alpha152_raw.tanh()
    
    return factor_value.over("symbol").cast(pl.Float32).alias("factor_110")
