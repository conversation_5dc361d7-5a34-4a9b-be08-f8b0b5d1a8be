# Alpha299因子 - factor_143 (Polars版本 - 准确实现)
# 原始因子编号: 143
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_143(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP计算、成交量和VWAP的Z-score、截面排名、滚动相关系数、滚动求和

    参数:
        w: 标准化窗口，默认20
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    zscore_window = w        # 标准化窗口
    correlation_window = max(1, w * 3 // 10)  # 相关性窗口
    sum_window = max(1, w // 10)              # 滚动求和窗口

    # 1. 计算ts_zscore(Volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_ma = Ops.rolling_mean(volume_protected, zscore_window)
    volume_std = Ops.rolling_std(volume_protected, zscore_window)
    ZV = ((volume_protected - volume_ma) / (volume_std + eps)).over("symbol").alias("_ZV")

    # 2. 计算ts_zscore(VWAP)
    vwap_ma = Ops.rolling_mean(pl.col("Vwap"), zscore_window)
    vwap_std = Ops.rolling_std(pl.col("Vwap"), zscore_window)
    ZVWAP = ((pl.col("Vwap") - vwap_ma) / (vwap_std + eps)).over("symbol").alias("_ZVWAP")

    # 3. 横截面百分比排名
    R_V = ((pl.col("_ZV").rank(method="average") / pl.len()).over("datetime")).alias("_R_V")
    R_VWAP = ((pl.col("_ZVWAP").rank(method="average") / pl.len()).over("datetime")).alias("_R_VWAP")

    # 4. 计算滚动相关系数
    C_t = Ops.rolling_corr(pl.col("_R_V"), pl.col("_R_VWAP"), correlation_window).over("symbol").alias("_C_t")

    # 5. 滚动求和
    S_C = Ops.rolling_sum(pl.col("_C_t"), sum_window).over("symbol").alias("_S_C")

    # 6. 横截面百分比排名
    factor_result = ((pl.col("_S_C").rank(method="average") / pl.len()).over("datetime")).cast(pl.Float32).alias("factor_143")

    return [ZV, ZVWAP, R_V, R_VWAP, C_t, S_C, factor_result]
