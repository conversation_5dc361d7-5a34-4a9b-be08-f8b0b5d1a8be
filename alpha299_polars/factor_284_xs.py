# Alpha299因子 - factor_284 (Polars版本 - 准确实现)
# 原始因子编号: 284
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_284(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及成交额与收盘价的滚动协方差、Z-score标准化、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    cov_window = w if w is not None else 5      # ts_cov窗口
    zscore_window = w if w is not None else 5   # ts_zscore窗口

    # 1. 计算滚动协方差 ts_cov(cov_window, amount, close)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    cov = Ops.rolling_cov(amount_protected, "Close", cov_window).over("symbol").alias("_cov")

    # 2. 滚动Z-score标准化 ts_zscore(zscore_window, cov)
    cov_mean = Ops.rolling_mean(pl.col("_cov"), zscore_window)
    cov_std = Ops.rolling_std(pl.col("_cov"), zscore_window)
    zscore = ((pl.col("_cov") - cov_mean) / (cov_std + eps)).over("symbol").alias("_zscore")

    # 3. 截面排名 rank(zscore)
    factor_result = (pl.col("_zscore").rank(method="average") / pl.len()).over("datetime").cast(pl.Float32).alias("factor_284")

    return [cov, zscore, factor_result]
