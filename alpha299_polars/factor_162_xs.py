# Alpha299因子 - factor_162 (Polars版本 - 准确实现)
# 原始因子编号: 162
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_162(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP差分、线性衰减、截面排名、滚动相关系数

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n_decay1 = w        # 第一次线性衰减窗口
    n_decay2 = w        # 第二次线性衰减窗口
    n_corr_window = w   # 相关系数窗口
    n_ma_window = w     # 移动平均窗口

    # 1. 计算VWAP的1期差分和对delta_vwap应用线性衰减加权求和
    delta_vwap = (pl.col("Vwap") - pl.col("Vwap").shift(1)).over("symbol").alias("_delta_vwap")
    dl1 = Ops.decaylinear(pl.col("_delta_vwap"), n_decay1).over("symbol").alias("_dl1")

    # 2. 横截面百分比排序R1
    r1 = ((pl.col("_dl1").rank(method="average") / pl.len()).over("datetime")).alias("_r1")

    # 3. 计算最低价(Low)与成交量滚动均值的滚动相关系数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    ma_volume = Ops.rolling_mean(volume_protected, n_ma_window)
    corr_lv = Ops.rolling_corr("Low", ma_volume, n_corr_window).over("symbol").alias("_corr_lv")

    # 4. 对corr_lv进行横截面百分比排序
    rank_corr_lv = ((pl.col("_corr_lv").rank(method="average") / pl.len()).over("datetime")).alias("_rank_corr_lv")

    # 5. 对rank_corr_lv应用线性衰减加权求和
    dl2 = Ops.decaylinear(pl.col("_rank_corr_lv"), n_decay2).over("symbol").alias("_dl2")

    # 6. 对dl2进行横截面百分比排序R2
    r2 = ((pl.col("_dl2").rank(method="average") / pl.len()).over("datetime")).alias("_r2")

    # 7. 取R1和R2的最大值并取负
    factor_result = (-pl.max_horizontal([pl.col("_r1"), pl.col("_r2")])).cast(pl.Float32).alias("factor_162")

    return [delta_vwap, dl1, r1, corr_lv, rank_corr_lv, dl2, r2, factor_result]
