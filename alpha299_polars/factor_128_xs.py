# Alpha299因子 - factor_128 (Polars版本 - 准确实现)
# 原始因子编号: 128
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_128(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及随机震荡指标、截面排名、成交量Z-score、滚动相关系数

    参数:
        w: 基础窗口大小，默认6
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    stochastic_window = 2 * w  # 随机指标窗口
    correlation_window = w     # 相关性窗口

    # 1. 计算滚动最低价和最高价，计算随机震荡值StochK
    L_low = Ops.rolling_min("Low", stochastic_window)
    H_high = Ops.rolling_max("High", stochastic_window)
    StochK = ((pl.col("Close") - L_low) / (H_high - L_low + eps)).over("symbol").alias("_StochK")

    # 2. 横截面百分比排序R_K
    R_K = ((pl.col("_StochK").rank(method="average") / pl.len()).over("datetime")).alias("_R_K")

    # 3. 计算成交量的滚动标准化Z_V
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, correlation_window)
    volume_std = Ops.rolling_std(volume_protected, correlation_window)
    Z_V = ((volume_protected - volume_mean) / (volume_std + eps)).over("symbol").alias("_Z_V")

    # 4. 横截面百分比排序R_V
    R_V = ((pl.col("_Z_V").rank(method="average") / pl.len()).over("datetime")).alias("_R_V")

    # 5. 计算R_K和R_V的滚动相关系数
    factor_result = Ops.rolling_corr(pl.col("_R_K"), pl.col("_R_V"), correlation_window).over("symbol").cast(pl.Float32).alias("factor_128")

    return [StochK, R_K, Z_V, R_V, factor_result]
