# Alpha299因子 - factor_335 (Polars版本)
# 原始因子编号: 335
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_335(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha125因子：基于amount-vwap协方差与amount-open回归贝塔均值最小值的Z-score截面排名乘以amount差分除以close标准差的最小值（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 9,   # ts_cov窗口
        'n2': 10,  # ts_regbeta窗口
        'n3': 4,   # ts_mean窗口
        'n4': 12,  # ts_zscore窗口
        'n5': 3,   # delta窗口
        'n6': 6,   # ts_std窗口
        'n7': 7    # ts_min窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']
    n6 = window_sizes['n6']
    n7 = window_sizes['n7']

    # 使用别名避免重复字段问题
    # 1. 计算T1 = ts_cov(amount, vwap, n1)
    T1 = Ops.rolling_cov("Amount", "Vwap", n1).alias("_T1")

    # 2. 计算T2 = ts_regbeta(amount, open, n2)
    T2 = Ops.rolling_regbeta("Amount", "Open", n2).alias("_T2")

    # 3. 计算T3 = ts_mean(T2, n3)
    T3 = Ops.rolling_mean(T2, n3).alias("_T3")

    # 4. 计算T4 = gp_min(T1, T3)
    T4 = pl.min_horizontal([T1, T3]).alias("_T4")

    # 5. 计算T4_zscored = ts_zscore(T4, n4)
    T4_mean = Ops.rolling_mean(T4, n4).alias("_T4_mean")
    T4_std = Ops.rolling_std(T4, n4).alias("_T4_std")
    T4_zscored = ((T4 - T4_mean) / (T4_std + eps)).alias("_T4_zscored")

    # 6. 简化实现：使用时序排名代替横截面排名
    X1 = (Ops.rolling_rank(T4_zscored, n4)).alias("_X1")

    # 7. 计算T5 = delta(amount, n5)
    T5 = (pl.col("Amount") - pl.col("Amount").shift(n5)).alias("_T5")

    # 8. 计算T6 = ts_std(close, n6)
    T6 = Ops.rolling_std("Close", n6).alias("_T6")

    # 9. 计算T7 = T5 / T6
    T7 = (T5 / (T6 + eps)).alias("_T7")

    # 10. 计算X2 = ts_min(T7, n7)
    X2 = Ops.rolling_min(T7, n7).alias("_X2")

    # 11. 计算Alpha125 = X1 * X2
    factor_result = X1 * X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_335")
