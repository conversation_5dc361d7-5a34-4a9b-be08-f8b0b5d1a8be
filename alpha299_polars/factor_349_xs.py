# Alpha299因子 - factor_349 (Polars版本 - 准确实现)
# 原始因子编号: 349
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_349(w: int | None = 5, uni_col: str | None = 'Close', eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及二阶差分、Z-score标准化、截面排名、滚动最大值、加法

    参数:
        w: 基础窗口大小，默认5
        uni_col: 单一基础数据列，默认为'Close'
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta1_window = 16      # 第一个delta的窗口
    delta2_window = 5       # 第二个delta的窗口
    ts_zscore_window = 20   # ts_zscore的窗口
    ts_max_window = 14      # ts_max的窗口

    # 1. 计算收盘价的delta1_window期差值
    delta1 = (pl.col(uni_col) - pl.col(uni_col).shift(delta1_window)).over("symbol").alias("_delta1")

    # 2. 计算delta1的delta2_window期差值（二阶差分）
    delta2 = (pl.col("_delta1") - pl.col("_delta1").shift(delta2_window)).over("symbol").alias("_delta2")

    # 3. 对delta2进行ts_zscore_window期滚动Z-score标准化
    delta2_mean = Ops.rolling_mean(pl.col("_delta2"), ts_zscore_window)
    delta2_std = Ops.rolling_std(pl.col("_delta2"), ts_zscore_window)
    zscore_delta2 = ((pl.col("_delta2") - delta2_mean) / (delta2_std + eps)).over("symbol").alias("_zscore_delta2")

    # 4. 对成交额进行截面排名
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    rank_amount = (amount_protected.rank(method="average") / pl.len()).over("datetime").alias("_rank_amount")

    # 5. 计算rank_amount在过去ts_max_window期的滚动最大值
    ts_max_rank = Ops.rolling_max(pl.col("_rank_amount"), ts_max_window).over("symbol").alias("_ts_max_rank")

    # 6. 计算zscore_delta2与ts_max_rank的和
    factor_result = (pl.col("_zscore_delta2") + pl.col("_ts_max_rank")).cast(pl.Float32).alias("factor_349")

    return [delta1, delta2, zscore_delta2, rank_amount, ts_max_rank, factor_result]
