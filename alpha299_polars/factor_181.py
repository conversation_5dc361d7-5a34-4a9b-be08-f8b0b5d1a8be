# Alpha299因子 - factor_181 (Polars版本)
# 原始因子编号: 181
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_181(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha85因子：成交量强度与价格动量时序排名乘积因子
    
    参数:
        w: 基准时间窗口参数（单位：天），默认7
        uni_col: 单一基础数据列参数，此处设为None（因子涉及多个基础列）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delta_days': 7,      # w * 0.35 = 20 * 0.35 = 7天差分窗口
        'ts_rank_window1': 20,  # w = 20天卷积排名窗口
        'ts_rank_window2': 8   # w * 0.4 = 20 * 0.4 = 8天动量排名窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delta_days = window_sizes['delta_days']
    ts_rank_window1 = window_sizes['ts_rank_window1']
    ts_rank_window2 = window_sizes['ts_rank_window2']

    # 1. 计算成交量均值
    volume_ma20 = Ops.rolling_mean("Volume", ts_rank_window1)

    # 2. 计算成交量比率
    vol_ratio = pl.col("Volume") / (volume_ma20 + eps)

    # 3. 计算时序排名 P1
    p1 = Ops.rolling_rank(vol_ratio, ts_rank_window1)

    # 4. 计算价格差分
    delta_7 = pl.col("Close") - pl.col("Close").shift(delta_days)

    # 5. 取负值并计算时序排名 P2
    delta_neg = -delta_7
    p2 = Ops.rolling_rank(delta_neg, ts_rank_window2)

    # 6. 计算最终因子值
    factor_result = p1 * p2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_181")
