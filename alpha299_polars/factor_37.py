# Alpha299因子 - factor_37 (Polars版本)
# 原始因子编号: 37
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_37(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha72因子：基于价格位置与成交量相关性的复合因子
    
    参数:
        w: 基准参数（本因子使用固定窗口，设为None）
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 因子 Alpha72 的核心参数定义（固定值）
    n1 = int(8.93345)  # correlation(High+Low/2, ADV40) 窗口
    n2 = int(10.1519)  # decay_linear 窗口
    n3 = int(3.72469)  # ts_rank(VWAP) 窗口
    n4 = int(18.5188)  # ts_rank(VOLUME) 窗口
    n5 = int(6.86671)  # correlation(ts_rank(VWAP), ts_rank(VOLUME)) 窗口
    n6 = int(2.95011)  # decay_linear 窗口

    # 计算ADV40（40日平均成交量）
    adv40 = Ops.rolling_mean("Volume", 40)

    # 计算(High+Low)/2
    hl2 = (pl.col("High") + pl.col("Low")) / 2

    # 计算VWAP（成交量加权平均价）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算(High+Low)/2 与 ADV40 的相关系数（窗口n1）
    corr_hl2_adv40 = Ops.rolling_corr(hl2, adv40, n1)

    # 对相关系数进行线性衰减处理（窗口n2）
    decay_corr = Ops.decaylinear(corr_hl2_adv40, n2)

    # 对衰减后的结果进行滚动窗口排名
    rank_decay_corr = Ops.rolling_rank(decay_corr, n2)

    # 计算VWAP的ts_rank（窗口n3）
    vwap_ts_rank = Ops.rolling_rank(vwap, n3)

    # 计算VOLUME的ts_rank（窗口n4）
    volume_ts_rank = Ops.rolling_rank("Volume", n4)

    # 计算ts_rank(VWAP) 与 ts_rank(VOLUME) 的相关系数（窗口n5）
    corr_ts_rank = Ops.rolling_corr(vwap_ts_rank, volume_ts_rank, n5)

    # 对相关系数进行线性衰减处理（窗口n6）
    decay_corr_ts = Ops.decaylinear(corr_ts_rank, n6)

    # 对衰减后的结果进行滚动窗口排名
    rank_decay_corr_ts = Ops.rolling_rank(decay_corr_ts, n6)

    # 计算最终因子值（分子/分母），只在最后使用一次.over()
    factor_result = rank_decay_corr / (rank_decay_corr_ts + eps)

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_37")
