# Alpha299因子 - factor_420 (Polars版本)
# 原始因子编号: 420
# 转写时间: 2025-07-18

import polars as pl

def factor_420(w: int | None = None, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算输入序列的三角正切值
    
    参数:
        w: 窗口期参数，本因子不需要窗口期，设为None
        uni_col: 用于计算因子的数据列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算正切值
    # polars的tan函数会自动处理inf/-inf和nan
    return pl.col(uni_col).tan().alias("factor_420")
