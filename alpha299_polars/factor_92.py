# Alpha299因子 - factor_92 (Polars版本)
# 原始因子编号: 92
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_92(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂的相关性比较因子
    
    原始逻辑：
    1. 计算中间价 mid = (high + low) / 2
    2. 计算SumMid_20 = 20期滚动求和
    3. 计算MAVol_60 = 60期成交量移动平均
    4. 计算SumMAVol_20 = MAVol_60的20期滚动求和
    5. 计算Corr_1 = SumMid_20与SumMAVol_20的9期滚动相关系数
    6. 计算Corr_2 = low与volume的6期滚动相关系数
    7. 横截面百分比排序，生成因子值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为6天
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        List[pl.Expr]: 因子表达式列表，最后一个为"factor_92"
    """
    
    # 三段式混合模型窗口配置
    window_configs = {
        'base_window': 20,      # 基础窗口 w
        'n_mavol': 60,          # 3*w = 60
        'n_corr1': 9,           # 0.45*w = 9
        'n_corr2': 6,           # 0.3*w = 6
    }
    
    def calculate_window_sizes(w1_input):
        """
        三段式混合模型：动态窗口计算函数
        """
        # 核心参数
        w_max = 300.0
        lambda_rate = 0.1
        alpha = 1.0
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 主要逻辑：三段式混合模型
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else: # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                final_value = min(max(final_value, w), w_max)
                results[k] = max(1, int(final_value))
            
            return results
    
    # 使用动态窗口计算
    window_sizes = calculate_window_sizes(w)
    base_window = window_sizes['base_window']
    n_mavol = window_sizes['n_mavol']
    n_corr1 = window_sizes['n_corr1']
    n_corr2 = window_sizes['n_corr2']
    
    # 计算中间价
    mid = (pl.col("High") + pl.col("Low")) / (2 + eps)

    # 计算SumMid_20
    sum_mid_20 = Ops.rolling_sum(mid, base_window).over("symbol").alias("_sum_mid_20")

    # 计算MAVol_60
    mavol_60 = Ops.rolling_mean(pl.col("Volume"), n_mavol).over("symbol").alias("_mavol_60")

    # 计算SumMAVol_20
    sum_mavol_20 = Ops.rolling_sum(pl.col("_mavol_60"), base_window).over("symbol").alias("_sum_mavol_20")

    # 计算Corr_1 (9期滚动相关系数)
    corr1 = Ops.rolling_corr(pl.col("_sum_mid_20"), pl.col("_sum_mavol_20"), n_corr1).over("symbol").alias("_corr1")

    # 计算Corr_2 (6期滚动相关系数)
    corr2 = Ops.rolling_corr(pl.col("Low"), pl.col("Volume"), n_corr2).over("symbol").alias("_corr2")

    # 横截面百分比排序
    r1 = ((pl.col("_corr1").rank(method="average") / pl.len()).over("datetime")).alias("_r1")
    r2 = ((pl.col("_corr2").rank(method="average") / pl.len()).over("datetime")).alias("_r2")

    # 生成因子值
    factor_value = pl.when(pl.col("_r1") < pl.col("_r2")).then(-1).otherwise(0).cast(pl.Float32).alias("factor_92")

    return [sum_mid_20, mavol_60, sum_mavol_20, corr1, corr2, r1, r2, factor_value]
