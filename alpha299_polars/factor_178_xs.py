# Alpha299因子 - factor_178 (Polars版本 - 准确实现)
# 原始因子编号: 178
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_178(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP与收盘价差值、滚动最大最小值、截面排名、成交量差分、Z-score标准化

    参数:
        w: 滚动窗口大小，默认3
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    actual_w = w if w is not None else 3

    # 1. 计算VWAP与收盘价的差值Dt
    D_t = (pl.col("Vwap") - pl.col("Close")).alias("_D_t")

    # 2. 计算Dt的滚动最大值和最小值
    D_max3 = Ops.rolling_max(pl.col("_D_t"), actual_w).over("symbol").alias("_D_max3")
    D_min3 = Ops.rolling_min(pl.col("_D_t"), actual_w).over("symbol").alias("_D_min3")

    # 3. 对D_max3和D_min3进行横截面百分位排序
    R_Dmax = ((pl.col("_D_max3").rank(method="average") / pl.len()).over("datetime")).alias("_R_Dmax")
    R_Dmin = ((pl.col("_D_min3").rank(method="average") / pl.len()).over("datetime")).alias("_R_Dmin")

    # 4. 计算成交量的差分
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    delta_vol = (volume_protected - volume_protected.shift(actual_w)).over("symbol").alias("_delta_vol")

    # 5. 对delta_vol进行滚动标准化
    delta_vol_mean = Ops.rolling_mean(pl.col("_delta_vol"), actual_w)
    delta_vol_std = Ops.rolling_std(pl.col("_delta_vol"), actual_w)
    Z_delta_vol = ((pl.col("_delta_vol") - delta_vol_mean) / (delta_vol_std + eps)).over("symbol").alias("_Z_delta_vol")

    # 6. 对标准化后的Z_delta_vol进行横截面百分位排序
    R_delta_vol = ((pl.col("_Z_delta_vol").rank(method="average") / pl.len()).over("datetime")).alias("_R_delta_vol")

    # 7. 计算最终因子值
    factor_result = ((pl.col("_R_Dmax") + pl.col("_R_Dmin")) * pl.col("_R_delta_vol")).cast(pl.Float32).alias("factor_178")

    return [D_t, D_max3, D_min3, R_Dmax, R_Dmin, delta_vol, Z_delta_vol, R_delta_vol, factor_result]
