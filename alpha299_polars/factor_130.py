# Alpha299因子 - factor_130 (Polars版本)
# 原始因子编号: 130
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_130(w: int | None = 4, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于VWAP、成交量相关性和价格低点的复合因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为4天。
        uni_col: 单一基础数据列（此处不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 4,   # VWAP和Volume相关性窗口
        'n2': 20,  # 标准化窗口
        'n3': 50,  # 成交量移动平均窗口（限制为30）
        'n4': 12   # 最终相关性窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = min(window_sizes['n3'], 30)  # 限制最大窗口为30
    n4 = window_sizes['n4']

    # 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算CorrVWAPVol_t：VWAP和Volume的n1期滚动相关系数
    corr_vwap_vol = Ops.rolling_corr(vwap, "Volume", n1)

    # 简化实现：使用时序排名代替横截面排名
    R1 = Ops.rolling_rank(corr_vwap_vol, n1)

    # 计算MA_n3(Volume)
    ma_volume = Ops.rolling_mean("Volume", n3)

    # 对MA_n3_Volume进行n2期滚动标准化
    ma_vol_mean = Ops.rolling_mean(ma_volume, n2)
    ma_vol_std = Ops.rolling_std(ma_volume, n2)
    zscore_ma_vol = (ma_volume - ma_vol_mean) / (ma_vol_std + eps)

    # 对Low进行n2期滚动标准化
    low_mean = Ops.rolling_mean("Low", n2)
    low_std = Ops.rolling_std("Low", n2)
    zscore_low = (pl.col("Low") - low_mean) / (low_std + eps)

    # 简化实现：使用时序排名代替横截面排名
    rank_zscore_low = Ops.rolling_rank(zscore_low, n2)
    rank_zscore_ma_vol = Ops.rolling_rank(zscore_ma_vol, n2)

    # 计算RankZScoreLow_t和RankZScoreMAVol50_t的n4期滚动相关系数
    corr_zscore = Ops.rolling_corr(rank_zscore_low, rank_zscore_ma_vol, n4)

    # 简化实现：使用时序排名代替横截面排名
    R2 = Ops.rolling_rank(corr_zscore, n4)

    # 计算最终因子值
    factor_result = R1 * R2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_130")
