# Alpha299因子 - factor_473 (Polars版本)
# 原始因子编号: 473
# 转写时间: 2025-07-18

import polars as pl

def factor_473(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算蔡金累积/派发线 (Chaikin Accumulation/Distribution Line, ADL)
    
    参数:
        w: 该因子不需要窗口参数，设为None
        uni_col: 该因子不依赖单一数据列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算价格区间
    price_range = pl.col("High") - pl.col("Low")
    
    # 计算资金流量乘数 (Money Flow Multiplier, MFM)
    # MFM = ((Close - Low) - (High - Close)) / (High - Low)
    # 等价于 MFM = (2*Close - Low - High) / (High - Low)
    mfm = (2 * pl.col("Close") - pl.col("Low") - pl.col("High")) / (price_range + eps)
    
    # 计算资金流量 (Money Flow Volume, MFV)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(0)
    mfv = mfm * volume_protected
    
    # 计算累积/派发线 (Accumulation/Distribution Line, ADL)
    # 使用cum_sum计算累积值
    adl = mfv.cum_sum()
    
    # 只在最后使用一次.over()
    return adl.over("symbol").alias("factor_473")
