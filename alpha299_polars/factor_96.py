# Alpha299因子 - factor_96 (Polars版本)
# 原始因子编号: 96
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_96(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂的多步骤因子：涉及移动平均、相关系数、线性衰减、Z-score、横截面排序
    
    原始逻辑：
    1. 计算Mid_t = (high + low) / 2
    2. 计算40期成交量移动平均MA_40_volume
    3. 计算Mid_t与MA_40_volume的9期滚动相关系数Corr_1
    4. 对Corr_1应用10期线性衰减DL_1
    5. 横截面排序得到R_1
    6. 计算VWAP和Volume的20期Z-score
    7. 横截面排序Z-score得到RankStdVWAP_t和RankStdVol_t
    8. 计算两个排序的7期滚动相关系数Corr_2
    9. 对Corr_2应用3期线性衰减DL_2
    10. 横截面排序得到R_2
    11. 最终因子值 = R_1 / R_2
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为3天
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        List[pl.Expr]: 因子表达式列表，最后一个为"factor_96"
    """
    
    # 三段式混合模型窗口配置
    window_configs = {
        'ma_40_window': 40,     # MA(Volume, 40)
        'corr_9_window': 9,     # 9期滚动相关系数
        'decay_10_window': 10,  # 10期线性衰减
        'zscore_20_window': 20, # 20期Z-score
        'corr_7_window': 7,     # 7期滚动相关系数
        'decay_3_window': 3,    # 3期线性衰减
    }
    
    def calculate_window_sizes(w1_input):
        """
        三段式混合模型：动态窗口计算函数
        """
        # 核心参数
        w_max = 300.0
        lambda_rate = 0.1
        alpha = 1.0
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 主要逻辑：三段式混合模型
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else: # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                final_value = min(max(final_value, w), w_max)
                results[k] = max(1, int(final_value))
            
            return results
    
    # 使用动态窗口计算
    window_sizes = calculate_window_sizes(w)
    ma_40_window = window_sizes['ma_40_window']
    corr_9_window = window_sizes['corr_9_window']
    decay_10_window = window_sizes['decay_10_window']
    zscore_20_window = window_sizes['zscore_20_window']
    corr_7_window = window_sizes['corr_7_window']
    decay_3_window = window_sizes['decay_3_window']
    
    # Step 1: 计算Mid_t
    mid_t = ((pl.col("High") + pl.col("Low")) / 2).alias("_mid_t")
    
    # Step 2: 计算40期成交量移动平均
    ma_40_volume = Ops.rolling_mean(pl.col("Volume"), ma_40_window).over("symbol").alias("_ma_40_volume")
    
    # Step 3: 计算9期滚动相关系数
    corr_1 = Ops.rolling_corr(pl.col("_mid_t"), pl.col("_ma_40_volume"), corr_9_window).over("symbol").alias("_corr_1")
    
    # Step 4: 应用10期线性衰减
    dl_1 = Ops.decaylinear(pl.col("_corr_1"), decay_10_window).over("symbol").alias("_dl_1")
    
    # Step 5: 横截面排序R_1
    r_1 = ((pl.col("_dl_1").rank(method="average") / pl.len()).over("datetime")).alias("_r_1")
    
    # Step 6: 计算VWAP和Volume的20期Z-score
    # 计算VWAP的Z-score
    vwap_mean = Ops.rolling_mean(pl.col("Vwap"), zscore_20_window).over("symbol")
    vwap_std = Ops.rolling_std(pl.col("Vwap"), zscore_20_window).over("symbol")
    std_vwap_t = ((pl.col("Vwap") - vwap_mean) / (vwap_std + eps)).alias("_std_vwap_t")
    
    # 计算Volume的Z-score
    vol_mean = Ops.rolling_mean(pl.col("Volume"), zscore_20_window).over("symbol")
    vol_std = Ops.rolling_std(pl.col("Volume"), zscore_20_window).over("symbol")
    std_vol_t = ((pl.col("Volume") - vol_mean) / (vol_std + eps)).alias("_std_vol_t")
    
    # Step 7: 横截面排序Z-score
    rank_std_vwap_t = ((pl.col("_std_vwap_t").rank(method="average") / pl.len()).over("datetime")).alias("_rank_std_vwap_t")
    rank_std_vol_t = ((pl.col("_std_vol_t").rank(method="average") / pl.len()).over("datetime")).alias("_rank_std_vol_t")
    
    # Step 8: 计算7期滚动相关系数
    corr_2 = Ops.rolling_corr(pl.col("_rank_std_vwap_t"), pl.col("_rank_std_vol_t"), corr_7_window).over("symbol").alias("_corr_2")
    
    # Step 9: 应用3期线性衰减
    dl_2 = Ops.decaylinear(pl.col("_corr_2"), decay_3_window).over("symbol").alias("_dl_2")
    
    # Step 10: 横截面排序R_2
    r_2 = ((pl.col("_dl_2").rank(method="average") / pl.len()).over("datetime")).alias("_r_2")
    
    # Step 11: 计算最终因子值
    factor_value = (pl.col("_r_1") / (pl.col("_r_2") + eps)).cast(pl.Float32).alias("factor_96")
    
    return [mid_t, ma_40_volume, corr_1, dl_1, r_1, std_vwap_t, std_vol_t, 
            rank_std_vwap_t, rank_std_vol_t, corr_2, dl_2, r_2, factor_value]
