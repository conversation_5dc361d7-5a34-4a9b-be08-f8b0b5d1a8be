# Alpha299因子 - factor_93 (Polars版本)
# 原始因子编号: 93
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_93(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 12因子：开盘价与VWAP偏离及收盘价与VWAP偏离因子
    
    原始逻辑：
    1. 计算日线级VWAP = amount / volume
    2. 计算MA(VWAP,10) = VWAP的10期移动平均
    3. 计算D_Open = open - MA(VWAP,10)
    4. 计算D_Close_Abs = |close - daily_vwap|
    5. 横截面百分比排序，计算最终因子值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为10天
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        List[pl.Expr]: 因子表达式列表，最后一个为"factor_93"
    """
    
    # 三段式混合模型窗口配置
    window_configs = {
        'ma_window': 10,        # 移动平均窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三段式混合模型：动态窗口计算函数
        """
        # 核心参数
        w_max = 300.0
        lambda_rate = 0.1
        alpha = 1.0
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 主要逻辑：三段式混合模型
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else: # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                final_value = min(max(final_value, w), w_max)
                results[k] = max(1, int(final_value))
            
            return results
    
    # 使用动态窗口计算
    window_sizes = calculate_window_sizes(w)
    ma_window = window_sizes['ma_window']
    
    # 计算VWAP（使用现有的Vwap列，或者从Amount/Volume计算）
    # 假设数据中已有Vwap列，如果没有则使用Amount/Volume
    vwap = pl.col("Vwap")

    # 计算MA(VWAP,10)
    ma_vwap_10 = Ops.rolling_mean(vwap, ma_window).over("symbol").alias("_ma_vwap_10")

    # 计算D_Open = open - MA(VWAP,10)
    d_open = (pl.col("Open") - pl.col("_ma_vwap_10")).alias("_d_open")

    # 计算D_Close_Abs = |close - vwap|
    d_close_abs = (pl.col("Close") - vwap).abs().alias("_d_close_abs")

    # 横截面百分比排序
    p1 = ((pl.col("_d_open").rank(method="average") / pl.len()).over("datetime")).alias("_p1")
    p2 = ((pl.col("_d_close_abs").rank(method="average") / pl.len()).over("datetime") * -1).alias("_p2")

    # 计算最终因子值
    factor_value = (pl.col("_p1") * pl.col("_p2")).cast(pl.Float32).alias("factor_93")

    return [ma_vwap_10, d_open, d_close_abs, p1, p2, factor_value]
