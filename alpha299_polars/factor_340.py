# Alpha299因子 - factor_340 (Polars版本)
# 原始因子编号: 340
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_340(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha140因子：基于复杂的延迟、排名、反正切、百分比变化、最小值、回归残差等计算（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 3,   # delay窗口
        'n2': 8,   # 第一个pctchg窗口
        'n3': 5,   # 第二个pctchg窗口
        'n4': 10,  # 第三个pctchg窗口
        'n5': 5,   # regres窗口
        'rank_window': 20  # 时序排名窗口（简化实现）
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']
    rank_window = window_sizes['rank_window']

    # 使用别名避免重复字段问题
    # 1. T1 = delay(open, n1)
    T1 = pl.col("Open").shift(n1).alias("_T1")

    # 2. 简化实现：使用时序排名代替横截面排名
    T2 = Ops.rolling_rank("Vwap", rank_window).alias("_T2")

    # 3. T3 = arctan(T2)
    T3 = T2.arctan().alias("_T3")

    # 4. T4 = ts_pctchg(T3, n2)
    T3_shifted = T3.shift(n2).alias("_T3_shifted")
    T4 = ((T3 - T3_shifted) / (T3_shifted + eps)).alias("_T4")

    # 5. T5 = gp_min(T1, T4)
    T5 = pl.min_horizontal([T1, T4]).alias("_T5")

    # 6. T6 = ts_pctchg(T5, n3)
    T5_shifted = T5.shift(n3).alias("_T5_shifted")
    T6 = ((T5 - T5_shifted) / (T5_shifted + eps)).alias("_T6")

    # 7. T7 = ts_pctchg(T6, n4)
    T6_shifted = T6.shift(n4).alias("_T6_shifted")
    T7 = ((T6 - T6_shifted) / (T6_shifted + eps)).alias("_T7")

    # 8. 简化实现：使用简单的回归残差
    factor_result = Ops.rolling_regres(T5, T7, n5)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_340")
