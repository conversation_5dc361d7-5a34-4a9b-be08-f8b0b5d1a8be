# Alpha299因子 - factor_358 (Polars版本)
# 原始因子编号: 358
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_358(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha199因子：基于volume的Z-score加上amount-vwap协方差均值的Z-score
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 20,      # 滚动标准化窗口
        'cov_window': 8,          # 协方差窗口
        'mean_window': 6          # 均值窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    zscore_window = window_sizes['zscore_window']
    cov_window = window_sizes['cov_window']
    mean_window = window_sizes['mean_window']

    # 使用别名避免重复字段问题
    # 1. 计算amount与vwap的协方差
    cov_amount_vwap = Ops.rolling_cov("Amount", "Vwap", cov_window).alias("_cov_amount_vwap")

    # 2. 计算协方差的滚动均值
    mean_cov = Ops.rolling_mean(cov_amount_vwap, mean_window).alias("_mean_cov")

    # 3. 对volume进行滚动标准化
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    volume_mean = Ops.rolling_mean(volume_protected, zscore_window).alias("_volume_mean")
    volume_std = Ops.rolling_std(volume_protected, zscore_window).alias("_volume_std")
    volume_zscore = ((volume_protected - volume_mean) / (volume_std + eps)).alias("_volume_zscore")

    # 4. 对mean_cov进行滚动标准化
    mean_cov_mean = Ops.rolling_mean(mean_cov, zscore_window).alias("_mean_cov_mean")
    mean_cov_std = Ops.rolling_std(mean_cov, zscore_window).alias("_mean_cov_std")
    mean_cov_zscore = ((mean_cov - mean_cov_mean) / (mean_cov_std + eps)).alias("_mean_cov_zscore")

    # 5. 将两个Z-score相加
    factor_result = volume_zscore + mean_cov_zscore

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_358")
