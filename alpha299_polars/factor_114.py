# Alpha299因子 - factor_114 (Polars版本)
# 原始因子编号: 114
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_114(w: int | None = None, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha158因子：价格振幅与收盘价比率因子
    
    参数:
        w: 无时间窗口参数，设为None
        uni_col: 使用'Close'作为基础列
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算价格振幅与收盘价比率
    factor_result = (pl.col("High") - pl.col("Low")) / (pl.col(uni_col) + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_114")
