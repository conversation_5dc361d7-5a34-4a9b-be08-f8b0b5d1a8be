# Alpha299因子 - factor_102 (Polars版本)
# 原始因子编号: 102
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_102(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 140因子，基于时间序列滚动标准化、线性衰减加权和横截面排名等操作
    
    原始逻辑：
    1. 对open, low, high, close进行20期ts_zscore
    2. 计算排名组合项Term_1 = ts_rank(Z_open,8) + ts_rank(Z_low,8) - ts_rank(Z_high,8) - ts_rank(Z_close,8)
    3. 应用8期线性衰减加权DL1
    4. 对DL1进行横截面排名R1
    5. 计算收盘价的8期ts_rank
    6. 计算成交量60期均值的20期ts_rank
    7. 计算两者的8期滚动相关系数Corr_2
    8. 应用7期线性衰减加权DL2
    9. 计算DL2的3期ts_rank得到R2
    10. 最终因子值 = min(R1, R2)
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为3天
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        List[pl.Expr]: 因子表达式列表，最后一个为"factor_102"
    """
    
    # 定义所有窗口的基准值
    window_configs = {
        'n_decay1': 8,      # DecayLinear窗口1
        'n_decay2': 7,      # DecayLinear窗口2
        'n_rank1': 8,       # ts_rank窗口1
        'n_rank2': 20,      # ts_rank窗口2
        'n_corr': 8,        # corr窗口
        'n_rank3': 3,       # ts_rank窗口3
        'n_z': 20,          # ts_zscore窗口
        'mav_window': 60    # 成交量移动平均窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_decay1 = window_sizes['n_decay1']
    n_decay2 = window_sizes['n_decay2']
    n_rank1 = window_sizes['n_rank1']
    n_rank2 = window_sizes['n_rank2']
    n_corr = window_sizes['n_corr']
    n_rank3 = window_sizes['n_rank3']
    n_z = window_sizes['n_z']
    mav_window = window_sizes['mav_window']
    
    # Step 1: 对open, low, high, close进行ts_zscore
    # 计算Z-score：(x - rolling_mean) / rolling_std
    z_open_mean = Ops.rolling_mean(pl.col("Open"), n_z).over("symbol")
    z_open_std = Ops.rolling_std(pl.col("Open"), n_z).over("symbol")
    z_open = ((pl.col("Open") - z_open_mean) / (z_open_std + eps)).alias("_z_open")
    
    z_low_mean = Ops.rolling_mean(pl.col("Low"), n_z).over("symbol")
    z_low_std = Ops.rolling_std(pl.col("Low"), n_z).over("symbol")
    z_low = ((pl.col("Low") - z_low_mean) / (z_low_std + eps)).alias("_z_low")
    
    z_high_mean = Ops.rolling_mean(pl.col("High"), n_z).over("symbol")
    z_high_std = Ops.rolling_std(pl.col("High"), n_z).over("symbol")
    z_high = ((pl.col("High") - z_high_mean) / (z_high_std + eps)).alias("_z_high")
    
    z_close_mean = Ops.rolling_mean(pl.col("Close"), n_z).over("symbol")
    z_close_std = Ops.rolling_std(pl.col("Close"), n_z).over("symbol")
    z_close = ((pl.col("Close") - z_close_mean) / (z_close_std + eps)).alias("_z_close")
    
    # Step 2: 计算排名组合项Term_1
    ts_rank_z_open = Ops.rolling_rank(pl.col("_z_open"), n_rank1).over("symbol")
    ts_rank_z_low = Ops.rolling_rank(pl.col("_z_low"), n_rank1).over("symbol")
    ts_rank_z_high = Ops.rolling_rank(pl.col("_z_high"), n_rank1).over("symbol")
    ts_rank_z_close = Ops.rolling_rank(pl.col("_z_close"), n_rank1).over("symbol")
    
    term1 = (ts_rank_z_open + ts_rank_z_low - ts_rank_z_high - ts_rank_z_close).alias("_term1")
    
    # Step 3: 应用线性衰减加权
    dl1 = Ops.decaylinear(pl.col("_term1"), n_decay1).over("symbol").alias("_dl1")
    
    # Step 4: 对DL1进行横截面排名R1
    r1 = ((pl.col("_dl1").rank(method="average") / pl.len()).over("datetime")).alias("_r1")
    
    # Step 5: 计算收盘价的ts_rank
    tsr_close = Ops.rolling_rank(pl.col("Close"), n_rank1).over("symbol").alias("_tsr_close")
    
    # Step 6: 计算成交量均值的ts_rank
    mav_volume = Ops.rolling_mean(pl.col("Volume"), mav_window).over("symbol").alias("_mav_volume")
    tsr_mav = Ops.rolling_rank(pl.col("_mav_volume"), n_rank2).over("symbol").alias("_tsr_mav")
    
    # Step 7: 计算相关系数Corr_2
    corr2 = Ops.rolling_corr(pl.col("_tsr_close"), pl.col("_tsr_mav"), n_corr).over("symbol").alias("_corr2")
    
    # Step 8: 应用线性衰减加权
    dl2 = Ops.decaylinear(pl.col("_corr2"), n_decay2).over("symbol").alias("_dl2")
    
    # Step 9: 计算DL2的ts_rank
    r2 = Ops.rolling_rank(pl.col("_dl2"), n_rank3).over("symbol").alias("_r2")
    
    # Step 10: 计算最终因子值 - min(R1, R2)
    factor_value = pl.min_horizontal([pl.col("_r1"), pl.col("_r2")]).cast(pl.Float32).alias("factor_102")
    
    return [z_open, z_low, z_high, z_close, term1, dl1, r1, tsr_close, 
            mav_volume, tsr_mav, corr2, dl2, r2, factor_value]
