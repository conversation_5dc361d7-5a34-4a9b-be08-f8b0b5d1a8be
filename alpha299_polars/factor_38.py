# Alpha299因子 - factor_38 (Polars版本)
# 原始因子编号: 38
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_38(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格位置与成交量相关性的最小排名因子
    
    参数:
        w: 基准参数（本因子使用固定窗口，设为None）
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 固定窗口参数
    decay_n1 = int(20.0451)  # 第一部分线性衰减窗口
    corr_n = int(3.1614)     # 相关性计算窗口
    decay_n2 = int(5.64125)  # 第二部分线性衰减窗口

    # 计算VWAP（成交量加权平均价）
    # 简化计算，使用Amount/Volume作为近似
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算40日平均成交量 (ADV40)
    adv40 = Ops.rolling_mean("Volume", 40)

    # 计算 (HIGH + LOW)/2
    hl2 = (pl.col("High") + pl.col("Low")) / 2

    # 第一部分：(HIGH + LOW)/2 - VWAP
    part1 = hl2 - vwap

    # 线性衰减处理第一部分
    part1_decay = Ops.decaylinear(part1, decay_n1)

    # 第一部分的排名（归一化到0-1）
    part1_rank = Ops.rolling_rank(part1_decay, decay_n1)

    # 第二部分：(HIGH+LOW)/2 与 ADV40 的相关性
    corr = Ops.rolling_corr(hl2, adv40, corr_n)

    # 线性衰减处理相关性结果
    part2_decay = Ops.decaylinear(corr, decay_n2)

    # 第二部分的排名（归一化到0-1）
    part2_rank = Ops.rolling_rank(part2_decay, decay_n2)

    # 取两部分的较小排名值，只在最后使用一次.over()
    factor_result = pl.min_horizontal([part1_rank, part2_rank])

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_38")
