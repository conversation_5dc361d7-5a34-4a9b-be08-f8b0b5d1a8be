# Alpha299因子 - factor_144 (Polars版本 - 准确实现)
# 原始因子编号: 144
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_144(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及收益率、滚动求和、乘积、差分、截面排名

    参数:
        w: 滚动求和窗口，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    rolling_window = w      # 滚动求和窗口
    delta_window = 2 * w    # 差分窗口

    # 1. 计算收益率RET
    ret = (pl.col("Close") / pl.col("Close").shift(1) - 1).over("symbol").alias("_ret")

    # 2. 计算S_Open和S_Ret
    S_Open = Ops.rolling_sum("Open", rolling_window).over("symbol").alias("_S_Open")
    S_Ret = Ops.rolling_sum(pl.col("_ret"), rolling_window).over("symbol").alias("_S_Ret")

    # 3. 计算X_t
    X_t = (pl.col("_S_Open") * pl.col("_S_Ret")).alias("_X_t")

    # 4. 计算差分（X_t - X_{t-delta_window}）
    delta_X = (pl.col("_X_t") - pl.col("_X_t").shift(delta_window)).over("symbol").alias("_delta_X")

    # 5. 横截面百分比排序，最终因子值（取负）
    rank_pct = ((pl.col("_delta_X").rank(method="average") / pl.len()).over("datetime")).alias("_rank_pct")
    factor_result = (-pl.col("_rank_pct")).over("symbol").cast(pl.Float32).alias("factor_144")

    return [ret, S_Open, S_Ret, X_t, delta_X, rank_pct, factor_result]
