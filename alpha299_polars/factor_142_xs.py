# Alpha299因子 - factor_142 (Polars版本 - 准确实现)
# 原始因子编号: 142
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_142(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及开盘价差分、线性衰减、截面排名、相关系数等

    参数:
        w: 基础窗口大小，默认7
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    decay_linear_1 = 2 * w + 1   # DecayLinear窗口1
    correlation_window = 2 * w + 3  # 相关性窗口
    decay_linear_2 = w           # DecayLinear窗口2

    # 1. 计算Delta(OPEN, 1)和DL1 = DecayLinear(DELTA(OPEN, 1), decay_linear_1)
    delta_open = (pl.col("Open") - pl.col("Open").shift(1)).alias("_delta_open")
    DL1 = Ops.decaylinear(pl.col("_delta_open"), decay_linear_1).over("symbol").alias("_DL1")

    # 2. R1 = rank(DL1)
    R1 = ((pl.col("_DL1").rank(method="average") / pl.len()).over("datetime")).alias("_R1")

    # 3. 计算CORR(OPEN, VOLUME, correlation_window)和DL2 = DecayLinear(Corr_OV, decay_linear_2)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    Corr_OV = Ops.rolling_corr("Open", volume_protected, correlation_window).over("symbol").alias("_Corr_OV")
    DL2 = Ops.decaylinear(pl.col("_Corr_OV"), decay_linear_2).over("symbol").alias("_DL2")

    # 4. R2 = rank(DL2)
    R2 = ((pl.col("_DL2").rank(method="average") / pl.len()).over("datetime")).alias("_R2")

    # 5. 最终因子计算
    factor_result = (-pl.min_horizontal([pl.col("_R1"), pl.col("_R2")])).over("symbol").cast(pl.Float32).alias("factor_142")

    return [delta_open, DL1, R1, Corr_OV, DL2, R2, factor_result]
