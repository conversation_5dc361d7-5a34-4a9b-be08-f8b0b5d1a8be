# Alpha299因子 - factor_324 (Polars版本)
# 原始因子编号: 324
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_324(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha99因子：基于volume平方根与close排名和vwap最大值的协方差和high-vwap协方差对open-close最小值回归贝塔的最小值
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n_rank': 8,        # ts_rank窗口
        'n_cov1': 6,        # 第一个ts_cov窗口
        'n_cov2': 10,       # 第二个ts_cov窗口
        'n_regbeta': 14     # ts_regbeta窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n_rank = window_sizes['n_rank']
    n_cov1 = window_sizes['n_cov1']
    n_cov2 = window_sizes['n_cov2']
    n_regbeta = window_sizes['n_regbeta']

    # 使用别名避免重复字段问题
    # 1. 计算sqrt(|volume|)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    T1 = (volume_protected.abs().sqrt()).alias("_T1")

    # 2. 计算ts_rank(close, n_rank)
    T2 = (Ops.rolling_rank("Close", n_rank)).alias("_T2")

    # 3. 计算gp_max(T2, vwap)
    T3 = pl.max_horizontal([T2, pl.col("Vwap")]).alias("_T3")

    # 4. 计算ts_cov(T1, T3, n_cov1)
    X1 = Ops.rolling_cov(T1, T3, n_cov1).alias("_X1")

    # 5. 计算ts_cov(high, vwap, n_cov2)
    T4 = Ops.rolling_cov("High", "Vwap", n_cov2).alias("_T4")

    # 6. 计算gp_min(open, close)
    T5 = pl.min_horizontal([pl.col("Open"), pl.col("Close")]).alias("_T5")

    # 7. 计算ts_regbeta(T4, T5, n_regbeta)
    X2 = Ops.rolling_regbeta(T4, T5, n_regbeta).alias("_X2")

    # 8. 计算gp_min(X1, X2)
    factor_result = pl.min_horizontal([X1, X2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_324")
