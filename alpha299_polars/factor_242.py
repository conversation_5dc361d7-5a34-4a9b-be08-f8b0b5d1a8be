# Alpha299因子 - factor_242 (Polars版本)
# 原始因子编号: 242
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_242(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha170因子：基于close标准差和volume最大值的复合Z-score标准差因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ts_std_window': 5,        # close标准差窗口
        'ts_max_window': 10,       # volume最大值窗口 (2 * 5)
        'zscore_window': 14,       # Z-score计算窗口
        'final_std_window': 14     # 最终标准差窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    ts_std_window = window_sizes['ts_std_window']
    ts_max_window = window_sizes['ts_max_window']
    zscore_window = window_sizes['zscore_window']
    final_std_window = window_sizes['final_std_window']

    # 步骤1: 计算I_1 = ts_std(close, ts_std_window)
    I1 = Ops.rolling_std("Close", ts_std_window)

    # 步骤2: 计算I_2 = ts_max(volume, ts_max_window)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    I2 = Ops.rolling_max(volume_protected, ts_max_window)

    # 步骤3: I_3 = gp_max(I1, I2)
    I3 = pl.max_horizontal([I1, I2])

    # 步骤4: I_4 = I1 (与I1相同)
    I4 = I1

    # 步骤5: 计算I3和I4的Z-score
    I3_mean = Ops.rolling_mean(I3, zscore_window)
    I3_std = Ops.rolling_std(I3, zscore_window)
    I3_z = (I3 - I3_mean) / (I3_std + eps)

    I4_mean = Ops.rolling_mean(I4, zscore_window)
    I4_std = Ops.rolling_std(I4, zscore_window)
    I4_z = (I4 - I4_mean) / (I4_std + eps)

    # 步骤6: I_5 = I3_z + I4_z
    I5 = I3_z + I4_z

    # 步骤7: 最终ts_std(I5, final_std_window)
    factor_result = Ops.rolling_std(I5, final_std_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_242")
