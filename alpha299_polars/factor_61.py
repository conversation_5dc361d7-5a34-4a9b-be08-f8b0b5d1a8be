# Alpha299因子 - factor_61 (Polars版本)
# 原始因子编号: 61
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_61(w: int | None = 17, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格排名与成交量排名最大值的因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为17天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 17

    # 计算VWAP（简化为Amount/Volume）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算价格排名
    rank_vwap = Ops.rolling_rank(vwap, window)

    # 计算成交量排名
    rank_volume = Ops.rolling_rank("Volume", window)

    # 取最大值
    factor_result = pl.max_horizontal([rank_vwap, rank_volume])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_61")
