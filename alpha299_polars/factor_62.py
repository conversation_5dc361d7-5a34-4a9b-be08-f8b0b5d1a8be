# Alpha299因子 - factor_62 (Polars版本)
# 原始因子编号: 62
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_62(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于高价与成交量相关性排名的负值因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 9

    # 计算HIGH与VOLUME的相关性
    corr = Ops.rolling_corr("High", "Volume", window)

    # 对相关性进行排名
    rank_corr = Ops.rolling_rank(corr, window)

    # 取负值
    factor_result = -rank_corr

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_62")
