# Alpha299因子 - factor_101 (Polars版本)
# 原始因子编号: 101
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_101(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 137复杂条件价格变动因子
    
    原始逻辑：
    1. 计算前一日数据：C_t-1, O_t-1, L_t-1
    2. 计算分子项：PriceMove_t = (close - C_t-1) + (close - open)/2 + (C_t-1 - O_t-1)
    3. 计算波动幅度：VolAmp_t = max(|high - C_t-1|, |low - C_t-1|)
    4. 计算分子：N_t = 16 * PriceMove_t * VolAmp_t
    5. 计算分母项：TermA = |high - C_t-1|, TermB = |low - C_t-1|, TermC = |high - L_t-1|, TermD = |C_t-1 - O_t-1|
    6. 根据条件选择分母：
       - 如果 TermA > TermB 且 TermA > TermC: D_t = TermA + TermB/2 + TermD/4
       - 如果 TermB > TermC 且 TermB > TermA: D_t = TermB + TermA/2 + TermD/4
       - 否则: D_t = TermC + TermD/4
    7. 因子值 = N_t / D_t
    
    参数:
        w: 基准参数，本因子不使用窗口，设为None
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_101"
    """
    
    # 计算前一日数据
    c_t_minus_1 = pl.col("Close").shift(1).over("symbol")
    o_t_minus_1 = pl.col("Open").shift(1).over("symbol")
    l_t_minus_1 = pl.col("Low").shift(1).over("symbol")
    
    # 计算分子项
    price_move_t = (
        (pl.col("Close") - c_t_minus_1) +
        (pl.col("Close") - pl.col("Open")) / (2 + eps) +
        (c_t_minus_1 - o_t_minus_1)
    )
    
    vol_amp_t = pl.max_horizontal([
        (pl.col("High") - c_t_minus_1).abs(),
        (pl.col("Low") - c_t_minus_1).abs()
    ])
    
    n_t = 16 * price_move_t * vol_amp_t
    
    # 计算分母项
    term_a = (pl.col("High") - c_t_minus_1).abs()
    term_b = (pl.col("Low") - c_t_minus_1).abs()
    term_c = (pl.col("High") - l_t_minus_1).abs()
    term_d = (c_t_minus_1 - o_t_minus_1).abs()
    
    # 根据条件选择分母
    cond1 = (term_a > term_b) & (term_a > term_c)
    cond2 = (term_b > term_c) & (term_b > term_a)
    
    d_t = pl.when(cond1).then(
        term_a + term_b / (2 + eps) + term_d / (4 + eps)
    ).when(cond2).then(
        term_b + term_a / (2 + eps) + term_d / (4 + eps)
    ).otherwise(
        term_c + term_d / (4 + eps)
    )
    
    # 计算最终因子值
    factor_value = n_t / (d_t + eps)
    
    return factor_value.cast(pl.Float32).alias("factor_101")
