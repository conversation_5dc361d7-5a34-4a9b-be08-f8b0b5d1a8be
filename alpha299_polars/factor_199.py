# Alpha299因子 - factor_199 (Polars版本)
# 原始因子编号: 199
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_199(w: int | None = 11, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算优化后的Alpha10因子：基于收盘价和成交量的Z-score标准化，然后计算标准差
    
    参数:
        w: 核心可调参数，单位为天，默认为11
        uni_col: 用于指定单一基础数据列，此处设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 11,   # w，close的ts_zscore窗口
        'n2': 14,   # int(14*w/11) = int(14*11/11) = 14，volume的ts_zscore窗口
        'n3': 11,   # w，再次计算close的ts_zscore窗口
        'n4': 15    # int(15*w/11) = int(15*11/11) = 15，最终ts_std窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    # 1. 计算X1: close的ts_zscore(n1)
    close_mean = Ops.rolling_mean("Close", n1)
    close_std = Ops.rolling_std("Close", n1)
    X1 = (pl.col("Close") - close_mean) / (close_std + eps)

    # 2. 计算X2: volume的ts_zscore(n2)
    # 对volume进行保护，避免负值或零值
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, n2)
    volume_std = Ops.rolling_std(volume_protected, n2)
    X2 = (volume_protected - volume_mean) / (volume_std + eps)

    # 3. 计算X3: X1和X2的逐元素最大值
    X3 = pl.max_horizontal([X1, X2])

    # 4. 计算X4: X1（再次计算close的ts_zscore(n3)）
    # 这里实际上就是X1，因为n1=n3=11
    X4 = X1

    # 5. 计算X5: X3 + X4
    X5 = X3 + X4

    # 6. 计算X6: X5的ts_std(n4)
    factor_result = Ops.rolling_std(X5, n4)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_199")
