# Alpha299因子 - factor_355 (Polars版本)
# 原始因子编号: 355
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_355(w: int | None = 8, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha192因子：基于high对close绝对值标准差的回归贝塔
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为8天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'regbeta_window': 18,     # TS_REGBETA窗口
        'std_period': 8           # TS_STD窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    regbeta_window = window_sizes['regbeta_window']
    std_period = window_sizes['std_period']

    # 使用别名避免重复字段问题
    # 1. 计算close的绝对值
    abs_close = pl.col("Close").abs().alias("_abs_close")

    # 2. 计算abs_close的滚动标准差
    std_abs_close = Ops.rolling_std(abs_close, std_period).alias("_std_abs_close")

    # 3. 计算high对std_abs_close的回归贝塔
    factor_result = Ops.rolling_regbeta("High", std_abs_close, regbeta_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_355")
