# Alpha299因子 - factor_1 (Polars版本)
# 原始因子编号: 1
# 转写时间: 2025-07-17

import polars as pl
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_1(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha101因子：(CLOSE - OPEN) / ((HIGH - LOW) + 0.001)

    参数:
        w: 基准参数，本因子不使用窗口，设为None
        uni_col: 单一基础列参数，本因子不使用，设为None
        eps: 防止除零的小常数

    返回:
        pl.Expr: 因子表达式
    """
    # 计算因子值：(CLOSE - OPEN) / ((HIGH - LOW) + 0.001)
    return (
        (pl.col("Close") - pl.col("Open")) /
        (pl.col("High") - pl.col("Low") + 0.001 + eps)
    ).cast(pl.Float32).alias("factor_1")
