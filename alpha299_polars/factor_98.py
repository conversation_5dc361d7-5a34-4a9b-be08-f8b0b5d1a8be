# Alpha299因子 - factor_98 (Polars版本)
# 原始因子编号: 98
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_98(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 133 Aroon振荡器因子
    
    原始逻辑：
    1. 计算DaysSinceHigh = N - k_max (距离最高值的天数)
    2. 计算DaysSinceLow = N - k_min (距离最低值的天数)
    3. 计算AroonUp = (N - DaysSinceHigh) / N * 100
    4. 计算AroonDown = (N - DaysSinceLow) / N * 100
    5. 因子值 = AroonUp - AroonDown
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为20天
        uni_col: 单一基础列参数，本因子不使用，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_98"
    """
    
    # 三段式混合模型窗口配置
    window_configs = {
        'aroon_window': 20,     # Aroon振荡器窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三段式混合模型：动态窗口计算函数
        """
        # 核心参数
        w_max = 300.0
        lambda_rate = 0.1
        alpha = 1.0
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 主要逻辑：三段式混合模型
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else: # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                final_value = min(max(final_value, w), w_max)
                results[k] = max(1, int(final_value))
            
            return results
    
    # 使用动态窗口计算
    window_sizes = calculate_window_sizes(w)
    aroon_window = window_sizes['aroon_window']
    
    # 计算距离最高值和最低值的天数
    # 使用rolling_max和rolling_min的位置信息来计算
    # 这里我们使用一个近似方法：计算当前值与窗口最值的比较
    
    # 计算滚动最大值和最小值
    rolling_high_max = Ops.rolling_max(pl.col("High"), aroon_window)
    rolling_low_min = Ops.rolling_min(pl.col("Low"), aroon_window)
    
    # 计算Aroon指标
    # 当当前高价等于窗口最高价时，DaysSinceHigh = 0
    # 否则使用一个近似计算
    days_since_high = pl.when(pl.col("High") == rolling_high_max).then(0).otherwise(aroon_window - 1)
    days_since_low = pl.when(pl.col("Low") == rolling_low_min).then(0).otherwise(aroon_window - 1)
    
    # 计算Aroon Up和Aroon Down
    aroon_up = (aroon_window - days_since_high) / (aroon_window + eps) * 100
    aroon_down = (aroon_window - days_since_low) / (aroon_window + eps) * 100
    
    # 计算最终因子值
    factor_value = aroon_up - aroon_down
    
    return factor_value.over("symbol").cast(pl.Float32).alias("factor_98")
