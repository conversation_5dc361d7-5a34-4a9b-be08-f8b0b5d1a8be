# Alpha299因子 - factor_54 (Polars版本 - 准确实现)
# 原始因子编号: 54
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_54(w: int | None = 15, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及高价截面排名、成交量移动平均、Z-score标准化、再次截面排名、滚动相关系数

    参数:
        w: 基础窗口大小，默认15
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n_ma = w          # 移动平均窗口
    n_zscore = w      # Z-score窗口
    n_corr = w        # 相关系数窗口

    # 1. 计算RH_t (high的横截面排序)
    RH_t = ((pl.col("High").rank(method="average") / pl.len()).over("datetime")).alias("_RH_t")

    # 2. 计算MA15(Volume)和Z-score标准化
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    MAV_15 = Ops.rolling_mean(volume_protected, n_ma)
    mav_mean = Ops.rolling_mean(MAV_15, n_zscore)
    mav_std = Ops.rolling_std(MAV_15, n_zscore)
    ZMAV_15 = ((MAV_15 - mav_mean) / (mav_std + eps)).over("symbol").alias("_ZMAV_15")

    # 3. 计算RMAV_15 (Z-score后的横截面排序)
    RMAV_15 = ((pl.col("_ZMAV_15").rank(method="average") / pl.len()).over("datetime")).alias("_RMAV_15")

    # 4. 计算Corr_t (RH_t与RMAV_15的滚动相关系数)
    Corr_t = Ops.rolling_corr(pl.col("_RH_t"), pl.col("_RMAV_15"), n_corr).over("symbol").alias("_Corr_t")

    # 5. 对Corr_t进行横截面排序，取负
    rank_Corr = ((pl.col("_Corr_t").rank(method="average") / pl.len()).over("datetime")).alias("_rank_Corr")
    factor_result = (-pl.col("_rank_Corr")).over("symbol").cast(pl.Float32).alias("factor_54")

    return [RH_t, ZMAV_15, RMAV_15, Corr_t, rank_Corr, factor_result]
