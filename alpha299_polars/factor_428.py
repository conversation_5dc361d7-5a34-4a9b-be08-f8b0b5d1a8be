# Alpha299因子 - factor_428 (Polars版本)
# 原始因子编号: 428
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def ultosc_numba(high, low, close, p1, p2, p3):
    """
    使用Numba优化的终极振荡指标计算
    """
    n = len(high)
    if n < max(p1, p2, p3):
        return np.full(n, np.nan)
    
    ultosc = np.full(n, np.nan)
    
    # 计算真实低点和购买压力
    true_low = np.zeros(n)
    buying_pressure = np.zeros(n)
    true_range = np.zeros(n)
    
    for i in range(n):
        if i == 0:
            true_low[i] = low[i]
            buying_pressure[i] = close[i] - low[i]
            true_range[i] = high[i] - low[i]
        else:
            # 真实低点
            true_low[i] = min(low[i], close[i-1])
            # 购买压力
            buying_pressure[i] = close[i] - true_low[i]
            # 真实波幅
            true_range[i] = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )
    
    # 计算各周期的滚动和
    for i in range(max(p1, p2, p3) - 1, n):
        # 短周期
        sum_bp1 = 0.0
        sum_tr1 = 0.0
        for j in range(i - p1 + 1, i + 1):
            sum_bp1 += buying_pressure[j]
            sum_tr1 += true_range[j]
        
        # 中周期
        sum_bp2 = 0.0
        sum_tr2 = 0.0
        for j in range(i - p2 + 1, i + 1):
            sum_bp2 += buying_pressure[j]
            sum_tr2 += true_range[j]
        
        # 长周期
        sum_bp3 = 0.0
        sum_tr3 = 0.0
        for j in range(i - p3 + 1, i + 1):
            sum_bp3 += buying_pressure[j]
            sum_tr3 += true_range[j]
        
        # 计算比率
        ratio1 = sum_bp1 / (sum_tr1 + 1e-8)
        ratio2 = sum_bp2 / (sum_tr2 + 1e-8)
        ratio3 = sum_bp3 / (sum_tr3 + 1e-8)
        
        # 计算终极振荡指标
        ultosc[i] = 100.0 * ((4 * ratio1 + 2 * ratio2 + ratio3) / 7.0)
    
    return ultosc

def factor_428(w: int | None = 7, uni_col: str | None = None) -> pl.Expr:
    """
    计算终极振荡指标 (Ultimate Oscillator, ULTOSC)
    
    参数:
        w: 短周期参数，默认为7
        uni_col: 单一列参数，本因子不使用，设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'short_period': 7,
        'medium_period': 14,
        'long_period': 28
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    p1 = window_sizes['short_period']
    p2 = window_sizes['medium_period']
    p3 = window_sizes['long_period']
    
    def apply_ultosc(high_values, low_values, close_values):
        return ultosc_numba(high_values, low_values, close_values, p1, p2, p3)
    
    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low"), pl.col("Close")],
        function=lambda x: pl.Series(apply_ultosc(x[0].to_numpy(), x[1].to_numpy(), x[2].to_numpy()))
    ).over("symbol").alias("factor_428")
