# Alpha299因子 - factor_103 (Polars版本)
# 原始因子编号: 103
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_103(w: int | None = None, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 143因子：条件累积乘积因子
    
    原始逻辑：
    1. 计算收益率 Ret_t = Close_t / Close_{t-1} - 1
    2. 当Close_t > Close_{t-1}时，Term_t = Ret_t
    3. 否则Term_t = 1
    4. 因子值为Term的累积乘积
    
    参数:
        w: 基准参数，本因子不使用窗口，设为None
        uni_col: 单一基础列参数，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_103"
    """
    
    # 计算收益率
    close_lag = pl.col(uni_col).shift(1)
    ret = pl.col(uni_col) / (close_lag + eps) - 1
    
    # 计算Term：当Close_t > Close_{t-1}时，Term_t = Ret_t，否则Term_t = 1
    term = pl.when(pl.col(uni_col) > close_lag).then(ret).otherwise(1.0)
    
    # 处理第一个数据点（前序不存在时设为1）
    term = term.fill_null(1.0)
    
    # 计算累积乘积
    # 注意：这里需要使用over("symbol")来确保按股票分组计算累积乘积
    factor_value = term.cum_prod()

    return factor_value.over("symbol").cast(pl.Float32).alias("factor_103")
