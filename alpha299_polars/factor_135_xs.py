# Alpha299因子 - factor_135 (Polars版本 - 准确实现)
# 原始因子编号: 135
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_135(w: int | None = 2, uni_col: str | None = 'Close', eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及价格动量、Z-score、线性衰减、收益率累计等

    参数:
        w: 基础窗口大小，默认2
        uni_col: 单一基础数据列，默认为'Close'
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = w          # 价格动量差分窗口
    zscore_window = w         # Z-score标准化窗口
    volume_ma_window = w + 1  # 成交量移动平均窗口
    decay_window = w          # 线性衰减窗口
    ret_sum_window = 10 * w   # 收益率累计窗口

    # 1. 计算R'_1: rank_cs(ts_zscore(Delta(Close_t, delta_window), zscore_window))
    # 计算价格动量和滚动标准化
    delta_close = (pl.col(uni_col) - pl.col(uni_col).shift(delta_window))
    delta_mean = Ops.rolling_mean(delta_close, zscore_window)
    delta_std = Ops.rolling_std(delta_close, zscore_window)
    zscore_delta = ((delta_close - delta_mean) / (delta_std + eps)).over("symbol").alias("_zscore_delta")

    # 计算横截面排名
    R1 = ((pl.col("_zscore_delta").rank(method="average") / pl.len()).over("datetime")).alias("_R1")

    # 2. 计算R2: 1 - rank_cs(DecayLinear(V'_t, decay_window))
    # 计算V'_t = Volume_t / MA(Volume_t)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_ma = Ops.rolling_mean(volume_protected, volume_ma_window)
    V_t = (volume_protected / (volume_ma + eps)).alias("_V_t")

    # 计算DecayLinear(V'_t, decay_window)
    decay_V = Ops.decaylinear(pl.col("_V_t"), decay_window).over("symbol").alias("_decay_V")

    # 计算横截面排名的逆
    rank_decay_V = ((pl.col("_decay_V").rank(method="average") / pl.len()).over("datetime")).alias("_rank_decay_V")
    R2 = (1 - pl.col("_rank_decay_V")).alias("_R2")

    # 3. 计算R3: 1 + rank_cs(sum(Ret_t, ret_sum_window))
    # 计算Ret_t = pct_chg
    Ret_t = (pl.col(uni_col) / pl.col(uni_col).shift(1) - 1).alias("_Ret_t")

    # 计算累计收益率
    ret_sum = Ops.rolling_sum(pl.col("_Ret_t"), ret_sum_window).over("symbol").alias("_ret_sum")

    # 计算横截面排名加1
    rank_ret_sum = ((pl.col("_ret_sum").rank(method="average") / pl.len()).over("datetime")).alias("_rank_ret_sum")
    R3 = (1 + pl.col("_rank_ret_sum")).alias("_R3")

    # 4. 计算最终因子值
    factor_result = (-(pl.col("_R1") * pl.col("_R2") * pl.col("_R3"))).over("symbol").cast(pl.Float32).alias("factor_135")

    return [zscore_delta, R1, V_t, decay_V, rank_decay_V, R2, Ret_t, ret_sum, rank_ret_sum, R3, factor_result]
