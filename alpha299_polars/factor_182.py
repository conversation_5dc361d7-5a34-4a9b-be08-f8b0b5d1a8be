# Alpha299因子 - factor_182 (Polars版本)
# 原始因子编号: 182
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_182(w: int | None = 10, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha86条件价格加速因子
    
    参数:
        w: 核心时间窗口参数（单位：天），默认值10
        uni_col: 单一基础数据列，默认值'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'delay_10': 10,  # w，延迟10期
        'delay_20': 20   # 2*w，延迟20期
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    delay_10 = window_sizes['delay_10']
    delay_20 = min(window_sizes['delay_20'], 30)  # 限制最大窗口为30

    # 1. 计算延迟项
    delay_10_val = pl.col(uni_col).shift(delay_10)
    delay_20_val = pl.col(uni_col).shift(delay_20)

    # 2. 计算价格加速项
    acc = ((delay_20_val - delay_10_val) / (10 + eps)) - ((delay_10_val - pl.col(uni_col)) / (10 + eps))

    # 3. 计算前一天的收盘价
    close_shift_1 = pl.col(uni_col).shift(1)

    # 4. 应用条件判断
    factor_result = pl.when(acc > 0.25).then(-1).when(
        acc < 0
    ).then(1).otherwise(-(pl.col(uni_col) - close_shift_1))

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_182")
