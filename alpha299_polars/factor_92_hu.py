# Alpha299因子 - factor_92 (Polars版本)
# 原始因子编号: 92
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_92(w: int | None = 15, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格最大值与最小值的因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为15天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 15

    # 计算HIGH的最大值
    max_high = Ops.rolling_max("High", window)

    # 计算LOW的最小值
    min_low = Ops.rolling_min("Low", window)

    # 计算比值
    factor_result = max_high / (min_low + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_92")
