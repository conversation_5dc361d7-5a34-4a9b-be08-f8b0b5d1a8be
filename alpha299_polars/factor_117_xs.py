# Alpha299因子 - factor_117 (Polars版本 - 准确实现)
# 原始因子编号: 117
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_117(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及收益率、成交量移动平均、VWAP、复杂的乘积项、截面排名

    参数:
        w: 成交量移动平均窗口，默认20
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    ma_volume_window = w

    # 1. 计算收益率RET_t
    ret = (pl.col("Close") / pl.col("Close").shift(1) - 1).over("symbol").alias("_ret")

    # 2. 计算成交量移动平均
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    ma_volume = Ops.rolling_mean(volume_protected, ma_volume_window).over("symbol").alias("_ma_volume")

    # 3. 计算Term_t
    term = (-pl.col("_ret") * pl.col("_ma_volume") * pl.col("Vwap") * (pl.col("High") - pl.col("Close"))).alias("_term")

    # 4. 按时间分组计算横截面百分比排名
    factor_result = ((pl.col("_term").rank(method="average") / pl.len()).over("datetime")).cast(pl.Float32).alias("factor_117")

    return [ret, ma_volume, term, factor_result]
