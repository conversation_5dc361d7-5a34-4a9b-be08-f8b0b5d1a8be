# Alpha299因子 - factor_129 (Polars版本)
# 原始因子编号: 129
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_129(w: int | None = None, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha178因子：成交量加权1日价格变化率因子
    核心公式：(Close_t - Close_{t-1})/Close_{t-1} * Volume_t
    
    参数:
        w: 本因子不涉及天数参数，故默认为None
        uni_col: 使用的单一基础数据列，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算前一日收盘价
    prev_close = pl.col(uni_col).shift(1)

    # 计算1日价格变化率
    roc = (pl.col(uni_col) - prev_close) / (prev_close + eps)

    # 计算最终因子值：价格变化率 * 成交量
    factor_result = roc * pl.col("Volume")

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_129")
