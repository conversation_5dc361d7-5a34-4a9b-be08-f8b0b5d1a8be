# Alpha299因子 - factor_58 (Polars版本)
# 原始因子编号: 58
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_58(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha156因子：双重衰减排名最大值因子（简化版）
    
    参数:
        w: 基准参数（本因子使用固定窗口，设为None）
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算VWAP（简化为Amount/Volume）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算VWAP的5期差分
    delta_vwap = vwap - vwap.shift(5)

    # 对Delta VWAP进行3期线性衰减
    dl1 = Ops.decaylinear(delta_vwap, 3)

    # 简化实现：使用时序排名代替横截面排名
    r1 = Ops.rolling_rank(dl1, 10)

    # 计算混合价格P_mix
    p_mix = 0.15 * pl.col("Open") + 0.85 * pl.col("Low")

    # 计算混合价格的2期变化率
    p_mix_shifted = p_mix.shift(2)
    roc_pmix = (p_mix - p_mix_shifted) / (p_mix + eps)

    # 对-ROC进行3期线性衰减
    negative_roc = -roc_pmix
    dl2 = Ops.decaylinear(negative_roc, 3)

    # 简化实现：使用时序排名代替横截面排名
    r2 = Ops.rolling_rank(dl2, 10)

    # 计算最终因子值
    max_rank = pl.max_horizontal([r1, r2])
    factor_result = -max_rank

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_58")
