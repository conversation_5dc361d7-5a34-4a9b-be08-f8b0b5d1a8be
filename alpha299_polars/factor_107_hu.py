# Alpha299因子 - factor_107 (Polars版本)
# 原始因子编号: 107
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_107(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于开盘价与高价差值排名的负值因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 20

    # 计算开盘价与高价的差值
    oh_diff = pl.col("Open") - pl.col("High")

    # 对差值进行排名并取负
    rank_diff = Ops.rolling_rank(oh_diff, window)
    factor_result = -rank_diff

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_107")
