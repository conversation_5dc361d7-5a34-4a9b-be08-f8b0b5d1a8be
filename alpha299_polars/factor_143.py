# Alpha299因子 - factor_143 (Polars版本)
# 原始因子编号: 143
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_143(w: int | None = 2, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于VWAP和成交量标准化排名相关性的因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
        uni_col: 单一基础数据列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'zscore_window': 2,        # Z-score标准化窗口
        'correlation_window': 6,   # 相关系数计算窗口
        'sum_window': 12          # 滚动求和窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    zscore_window = window_sizes['zscore_window']
    correlation_window = window_sizes['correlation_window']
    sum_window = window_sizes['sum_window']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算成交量的Z-score标准化
    volume_mean = Ops.rolling_mean("Volume", zscore_window)
    volume_std = Ops.rolling_std("Volume", zscore_window)
    ZV = (pl.col("Volume") - volume_mean) / (volume_std + eps)

    # 3. 计算VWAP的Z-score标准化
    vwap_mean = Ops.rolling_mean(vwap, zscore_window)
    vwap_std = Ops.rolling_std(vwap, zscore_window)
    ZVWAP = (vwap - vwap_mean) / (vwap_std + eps)

    # 4. 简化实现：使用时序排名代替横截面排名
    R_V = Ops.rolling_rank(ZV, zscore_window)
    R_VWAP = Ops.rolling_rank(ZVWAP, zscore_window)
    # 5. 计算滚动相关系数
    C_t = Ops.rolling_corr(R_V, R_VWAP, correlation_window)

    # 6. 计算滚动求和
    S_C = Ops.rolling_sum(C_t, sum_window)

    # 7. 简化实现：使用时序排名代替横截面排名
    factor_result = Ops.rolling_rank(S_C, sum_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_143")
