# Alpha299因子 - factor_64 (Polars版本)
# 原始因子编号: 64
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_64(w: int | None = 4, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格变化排名的因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为4天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 4

    # 计算价格变化
    price_change = pl.col("Close") - pl.col("Open")

    # 对价格变化进行排名
    rank_change = Ops.rolling_rank(price_change, window)
    # 只在最后使用一次.over()
    return rank_change.over("symbol").cast(pl.Float32).alias("factor_64")
