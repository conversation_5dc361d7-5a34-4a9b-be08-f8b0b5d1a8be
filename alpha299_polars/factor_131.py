# Alpha299因子 - factor_131 (Polars版本)
# 原始因子编号: 131
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_131(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha185因子：开盘收盘价比率平方的负排名因子（简化版）
    
    参数:
        w: 时间窗口参数（本因子无需时间窗口，设为None）
        uni_col: 单一基础数据列（本因子使用open和close，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算开盘价与收盘价的比率
    ratio = pl.col("Open") / (pl.col("Close") + eps)

    # 2. 计算项Term_t
    term = -(1 - ratio) ** 2

    # 3. 简化实现：使用时序排名代替横截面排名
    # 使用滚动窗口进行排名
    window = 20  # 使用固定窗口进行时序排名
    factor_result = Ops.rolling_rank(term, window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_131")
