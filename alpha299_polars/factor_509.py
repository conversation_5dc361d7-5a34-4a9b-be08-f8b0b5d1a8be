# Alpha299因子 - factor_509 (Polars版本)
# 原始因子编号: 509
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def pvi_numba(close, volume, initial_value=100.0):
    """
    使用Numba优化的正量指标(PVI)计算
    """
    n = len(close)
    if n < 2:
        return np.full(n, np.nan)
    
    pvi = np.zeros(n)
    pvi[0] = initial_value
    
    for i in range(1, n):
        prev_close = close[i-1]
        curr_close = close[i]
        prev_volume = volume[i-1]
        curr_volume = volume[i]
        
        # 计算价格变化率
        if prev_close > 1e-8:
            price_change_ratio = (curr_close - prev_close) / prev_close
        else:
            price_change_ratio = 0.0
        
        # 检查价格变化率是否有效
        if np.isnan(price_change_ratio) or np.isinf(price_change_ratio):
            pvi[i] = pvi[i-1]
            continue
        
        # 检查成交量是否增加
        if curr_volume > prev_volume:
            # 成交量增加时，根据价格变化调整PVI
            pvi[i] = pvi[i-1] * (1 + price_change_ratio)
        else:
            # 成交量不增加时，PVI保持不变
            pvi[i] = pvi[i-1]
        
        # 对计算出的PVI值进行inf和nan的保护
        if np.isinf(pvi[i]) or np.isnan(pvi[i]):
            pvi[i] = pvi[i-1] if i > 0 else initial_value
    
    return pvi

def factor_509(w: int | None = None, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算正量指标 (Positive Volume Index, PVI)
    
    参数:
        w: 窗口期参数，对于PVI不适用，设为None
        uni_col: 用于计算的价格列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    def apply_pvi(close_values, volume_values):
        return pvi_numba(close_values, volume_values)
    
    return pl.map_batches(
        exprs=[pl.col(uni_col), pl.col("Volume")],
        function=lambda x: pl.Series(apply_pvi(x[0].to_numpy(), x[1].to_numpy()))
    ).over("symbol").alias("factor_509")
