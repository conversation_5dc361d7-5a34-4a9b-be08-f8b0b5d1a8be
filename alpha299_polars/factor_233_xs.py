# Alpha299因子 - factor_233 (Polars版本 - 准确实现)
# 原始因子编号: 233
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_233(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP、移动平均、除法、最大值、协方差、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    mean_window = w if w is not None else 5  # 移动平均窗口
    cov_window = w if w is not None else 5   # 协方差窗口

    # 1. 计算T1 = neg(vwap)
    T1 = (-pl.col("Vwap")).alias("_T1")

    # 2. 计算amount的滚动均值
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    T2 = Ops.rolling_mean(amount_protected, mean_window).over("symbol").alias("_T2")

    # 3. X1 = T1 / T2
    X1 = (pl.col("_T1") / (pl.col("_T2") + eps)).alias("_X1")

    # 4. 计算close和vwap的逐元素最大值
    T3 = pl.max_horizontal([pl.col("Close"), pl.col("Vwap")]).alias("_T3")

    # 5. 计算T3和volume的滚动协方差
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    T4 = Ops.rolling_cov(pl.col("_T3"), volume_protected, cov_window).over("symbol").alias("_T4")

    # 6. 计算截面排名（按时间点）
    X2 = (pl.col("_T4").rank(method="average") / pl.len()).over("datetime").alias("_X2")

    # 7. 最终因子计算
    factor_result = (pl.col("_X1") / (pl.col("_X2") + eps)).cast(pl.Float32).alias("factor_233")

    return [T1, T2, X1, T3, T4, X2, factor_result]
