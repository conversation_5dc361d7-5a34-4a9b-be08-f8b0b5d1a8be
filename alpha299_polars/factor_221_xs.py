# Alpha299因子 - factor_221 (Polars版本 - 准确实现)
# 原始因子编号: 221
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_221(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算因子：涉及成交量与高价的滚动协方差、截面排名

    参数:
        w: 协方差窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    cov_window = w if w is not None else 5

    # 1. 计算滚动协方差 ts_cov(cov_window, volume, high)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    X1 = Ops.rolling_cov(volume_protected, "High", cov_window).over("symbol").alias("_X1")

    # 2. 截面排名 rank(X1)
    factor_result = (pl.col("_X1").rank(method="average") / pl.len()).over("datetime").cast(pl.Float32).alias("factor_221")

    return [X1, factor_result]
