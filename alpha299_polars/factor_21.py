# Alpha299因子 - factor_21 (Polars版本)
# 原始因子编号: 21
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_21(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha42因子：VWAP与CLOSE的差值排名除以VWAP与CLOSE的和的排名
    
    参数:
        w: 基准参数（本因子不使用窗口，设为None）
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算VWAP与CLOSE的差值和和
    vdiff = pl.col("Vwap") - pl.col("Close")
    vsum = pl.col("Vwap") + pl.col("Close")

    # 计算横截面排名（在同一时间点datetime内排名）
    rank_diff = vdiff.rank(method="dense", descending=True).over(["datetime"]) / pl.count().over(["datetime"])
    rank_sum = vsum.rank(method="dense", descending=True).over(["datetime"]) / pl.count().over(["datetime"])

    # 计算最终因子值：差值排名除以和的排名
    return (rank_diff / (rank_sum + eps)).cast(pl.Float32).alias("factor_21")
