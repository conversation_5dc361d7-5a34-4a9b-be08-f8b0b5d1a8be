# Alpha299因子 - factor_200 (Polars版本)
# 原始因子编号: 200
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_200(w: int | None = 12, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha111因子：基于成交量与开盘价/收盘价的相关性、成交金额平方根等的复合计算
    
    参数:
        w: 基准窗口参数（单位：天），默认为12
        uni_col: 单一基础列参数，本因子不需要使用该参数
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 12,   # w，ts_corr(12, volume, open_price)
        'n2': 18,   # int(1.5*w) = int(1.5*12) = 18，ts_max(C1, 18)
        'n3': 12,   # w，ts_corr(12, volume, close)
        'n4': 10    # int(0.833*w) = int(0.833*12) = 10，delay(T2, 10)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']

    # 步骤1: 计算volume和open的n1周期滚动相关系数
    C1 = Ops.rolling_corr("Volume", "Open", n1)

    # 步骤2: 计算C1的n2周期滚动最大值
    X1 = Ops.rolling_max(C1, n2)

    # 步骤3: 计算amount的绝对值平方根
    T1 = (pl.col("Amount").abs() + eps).sqrt()

    # 步骤4: 计算volume和close的n3周期滚动相关系数
    C2 = Ops.rolling_corr("Volume", "Close", n3)

    # 步骤5: 取T1和C2的较大值
    T2 = pl.max_horizontal([T1, C2])

    # 步骤6: 获取T2的n4周期延迟
    X2 = T2.shift(n4)

    # 步骤7: 计算X1和X2的乘积
    factor_result = X1 * X2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_200")
