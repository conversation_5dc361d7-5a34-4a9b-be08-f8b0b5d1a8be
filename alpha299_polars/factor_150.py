# Alpha299因子 - factor_150 (Polars版本)
# 原始因子编号: 150
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_150(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha45因子：价格动量与VWAP成交量相关性排名乘积因子（简化版）
    
    参数:
        w: 核心可调参数（此处因子中无天数参数，设为None）
        uni_col: 基础数据列（此处因子使用多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算VWAP（成交量加权平均价）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算混合价格Pmix
    pmix = 0.6 * pl.col("Close") + 0.4 * pl.col("Open")

    # 3. 计算混合价格1期差分
    delta_pmix = pmix - pmix.shift(1)

    # 4. 简化实现：使用时序排名代替横截面排名
    r1 = Ops.rolling_rank(delta_pmix, 20)

    # 5. 计算成交量的移动平均（限制窗口为30）
    mavol = Ops.rolling_mean("Volume", 30)

    # 6. 计算MAVol与VWAP的滚动相关系数
    corr_volvwap = Ops.rolling_corr(mavol, vwap, 15)

    # 7. 简化实现：使用时序排名代替横截面排名
    r2 = Ops.rolling_rank(corr_volvwap, 15)

    # 8. 计算最终因子值（R1 * R2）
    factor_result = r1 * r2

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_150")
