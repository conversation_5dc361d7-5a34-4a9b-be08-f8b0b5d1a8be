# Alpha299因子 - factor_77 (Polars版本)
# 原始因子编号: 77
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_77(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于价格最小值与成交量最大值的因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 20

    # 计算LOW的最小值
    min_low = Ops.rolling_min("Low", window)

    # 计算VOLUME的最大值
    max_volume = Ops.rolling_max("Volume", window)

    # 计算比值
    factor_result = min_low / (max_volume + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_77")
