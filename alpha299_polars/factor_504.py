# Alpha299因子 - factor_504 (Polars版本)
# 原始因子编号: 504
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def nvi_numba(close, volume, initial_value=1000.0):
    """
    使用Numba优化的负成交量指标(NVI)计算
    """
    n = len(close)
    if n < 2:
        return np.full(n, np.nan)
    
    nvi = np.zeros(n)
    nvi[0] = initial_value
    
    for i in range(1, n):
        prev_close = close[i-1]
        curr_close = close[i]
        prev_volume = volume[i-1]
        curr_volume = volume[i]
        
        # 根据NVI计算规则
        if np.isnan(prev_close) or prev_close <= 1e-8:
            # 如果前一天收盘价无效，则NVI保持不变
            nvi[i] = nvi[i-1]
        elif curr_volume < prev_volume:
            # 当天成交量小于前一天
            nvi[i] = nvi[i-1] * (curr_close / prev_close)
        else:
            # 当天成交量大于或等于前一天
            nvi[i] = nvi[i-1]
        
        # 对计算出的NVI值进行inf和nan的保护
        if np.isinf(nvi[i]) or np.isnan(nvi[i]):
            nvi[i] = nvi[i-1] if i > 0 else initial_value
    
    return nvi

def factor_504(w: int | None = None, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算负成交量指标 (Negative Volume Index, NVI)
    
    参数:
        w: 此因子不需要窗口参数，设为None
        uni_col: 用于计算的价格列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    def apply_nvi(close_values, volume_values):
        return nvi_numba(close_values, volume_values)
    
    return pl.map_batches(
        exprs=[pl.col(uni_col), pl.col("Volume")],
        function=lambda x: pl.Series(apply_nvi(x[0].to_numpy(), x[1].to_numpy()))
    ).over("symbol").alias("factor_504")
