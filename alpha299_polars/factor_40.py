# Alpha299因子 - factor_40 (Polars版本)
# 原始因子编号: 40
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_40(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于开盘价和收益率乘积的标准化排名因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'main_window': 5        # 主窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    main_window = window_sizes['main_window']

    # 参数联动
    sum_window = w if w is not None else main_window
    delay_window = 2 * sum_window
    z_window = 4 * sum_window

    # 计算收益率（RETURN）
    returns = (pl.col("Close") / pl.col("Close").shift(1) - 1)

    # 计算OPEN和RETURN的sum_window期累和
    open_sum = Ops.rolling_sum("Open", sum_window)
    return_sum = Ops.rolling_sum(returns, sum_window)

    # 计算乘积
    product = open_sum * return_sum

    # 计算delay_window期延迟值
    delayed_product = product.shift(delay_window)

    # 计算差值
    difference = product - delayed_product

    # 计算z_window期滚动标准化值（Z-score）
    difference_mean = Ops.rolling_mean(difference, z_window)
    difference_std = Ops.rolling_std(difference, z_window)
    z_score = (difference - difference_mean) / (difference_std + eps)

    # 使用滚动窗口排名
    rank = Ops.rolling_rank(z_score, z_window)

    # 取负作为最终因子值，只在最后使用一次.over()
    factor_result = -rank

    return factor_result.over("symbol").cast(pl.Float32).alias("factor_40")
