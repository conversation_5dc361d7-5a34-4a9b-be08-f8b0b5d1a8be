# Alpha299因子 - factor_314 (Polars版本)
# 原始因子编号: 314
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_314(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha82因子：基于复杂的Z-score计算、截面排名和协方差（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rank_window': 5,        # ts_rank窗口
        'cov_window': 6,         # ts_cov窗口
        'zscore_window': 20      # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    rank_window = window_sizes['rank_window']
    cov_window = window_sizes['cov_window']
    zscore_window = window_sizes['zscore_window']

    # 使用别名避免重复字段问题
    # 1. 计算low和volume的Z-score
    low_mean = Ops.rolling_mean("Low", zscore_window).alias("_low_mean")
    low_std = Ops.rolling_std("Low", zscore_window).alias("_low_std")
    low_z = ((pl.col("Low") - low_mean) / (low_std + eps)).alias("_low_z")

    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps).alias("_vol_protected")
    volume_mean = Ops.rolling_mean(volume_protected, zscore_window).alias("_vol_mean")
    volume_std = Ops.rolling_std(volume_protected, zscore_window).alias("_vol_std")
    volume_z = ((volume_protected - volume_mean) / (volume_std + eps)).alias("_vol_z")

    # 2. 计算gp_max(low_z, volume_z)
    T1 = pl.max_horizontal([low_z, volume_z]).alias("_T1")

    # 3. 计算ts_rank(T1, rank_window)
    X1 = Ops.rolling_rank(T1, rank_window).alias("_X1")

    # 4. 简化实现：使用时序排名代替横截面排名
    T2 = Ops.rolling_rank(volume_z, rank_window).alias("_T2")

    # 5. 计算high和vwap的Z-score
    high_mean = Ops.rolling_mean("High", zscore_window).alias("_high_mean")
    high_std = Ops.rolling_std("High", zscore_window).alias("_high_std")
    high_z = ((pl.col("High") - high_mean) / (high_std + eps)).alias("_high_z")

    vwap_mean = Ops.rolling_mean("Vwap", zscore_window).alias("_vwap_mean")
    vwap_std = Ops.rolling_std("Vwap", zscore_window).alias("_vwap_std")
    vwap_z = ((pl.col("Vwap") - vwap_mean) / (vwap_std + eps)).alias("_vwap_z")

    close_mean = Ops.rolling_mean("Close", zscore_window).alias("_close_mean")
    close_std = Ops.rolling_std("Close", zscore_window).alias("_close_std")
    close_z = ((pl.col("Close") - close_mean) / (close_std + eps)).alias("_close_z")

    # 6. 计算T3 = high_z * volume_z
    T3 = (high_z * volume_z).alias("_T3")

    # 7. 计算T4 = T2 / T3
    T4 = (T2 / (T3 + eps)).alias("_T4")

    # 8. 计算T5 = vwap_z + close_z
    T5 = (vwap_z + close_z).alias("_T5")

    # 9. 计算X2 = gp_max(T4, T5)
    X2 = pl.max_horizontal([T4, T5]).alias("_X2")

    # 10. 计算ts_cov(cov_window, X1, X2)
    factor_result = Ops.rolling_cov(X1, X2, cov_window)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_314")
