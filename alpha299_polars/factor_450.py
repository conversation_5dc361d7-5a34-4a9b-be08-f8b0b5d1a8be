# Alpha299因子 - factor_450 (Polars版本)
# 原始因子编号: 450
# 转写时间: 2025-07-18

import polars as pl

def factor_450(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算向量除法因子
    
    参数:
        w: 窗口参数，本因子不需要窗口，设为None
        uni_col: 单一列参数，本因子需要两列数据进行计算，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 执行向量除法运算: close / volume
    # 为了避免除以0导致无穷大，对volume加上一个很小的数
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    
    return (pl.col("Close") / volume_protected).alias("factor_450")
