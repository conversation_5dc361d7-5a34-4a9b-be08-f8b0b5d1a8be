# Alpha299因子 - factor_507 (Polars版本)
# 原始因子编号: 507
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def plus_dm_numba(high, low, window):
    """
    使用Numba优化的正向趋向移动(+DM)计算
    """
    n = len(high)
    if n < window:
        return np.full(n, np.nan)
    
    plus_dm = np.full(n, np.nan)
    
    # 计算单周期的+DM1
    dm1 = np.zeros(n)
    
    for i in range(1, n):
        # 计算上升和下降移动
        up_move = high[i] - high[i-1]
        down_move = low[i-1] - low[i]
        
        # 计算+DM1
        if up_move > down_move and up_move > 0:
            dm1[i] = up_move
        else:
            dm1[i] = 0.0
    
    # 使用Wilder平滑法计算+DM
    if n >= window:
        # 计算第一个平滑值（前window个值的和）
        first_sum = np.sum(dm1[:window])
        plus_dm[window-1] = first_sum
        
        # 使用递归公式计算后续值
        for i in range(window, n):
            if np.isfinite(plus_dm[i-1]) and np.isfinite(dm1[i]):
                plus_dm[i] = plus_dm[i-1] - (plus_dm[i-1] / window) + dm1[i]
            else:
                plus_dm[i] = np.nan
    
    return plus_dm

def factor_507(w: int | None = 14, uni_col: str | None = None) -> pl.Expr:
    """
    计算正向趋向移动 (Plus Directional Movement, +DM) 因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为14天
        uni_col: 本因子不使用单一列，设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'plus_dm_window': 14
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    plus_dm_window = window_sizes['plus_dm_window']
    
    def apply_plus_dm(high_values, low_values):
        return plus_dm_numba(high_values, low_values, plus_dm_window)
    
    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low")],
        function=lambda x: pl.Series(apply_plus_dm(x[0].to_numpy(), x[1].to_numpy()))
    ).over("symbol").alias("factor_507")
