# Alpha299因子 - factor_122 (Polars版本)
# 原始因子编号: 122
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_122(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于VWAP、成交量和高价的复合因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为5天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ma_volume_window': 20,  # TermA的MA窗口
        'ma_high_window': 5      # TermB的MA窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    w_ma_volume = window_sizes['ma_volume_window']
    w_ma_high = window_sizes['ma_high_window']

    # 计算VWAP（简化为Amount/Volume）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 简化实现：使用时序排名代替横截面排名
    # 计算TermA
    inv_close = 1 / (pl.col("Close") + eps)
    R_1_C = Ops.rolling_rank(inv_close, w_ma_volume)
    MA_volume = Ops.rolling_mean("Volume", w_ma_volume)
    TermA = (pl.col("Volume") * R_1_C) / (MA_volume + eps)

    # 计算TermB
    high_minus_close = pl.col("High") - pl.col("Close")
    R_HC = Ops.rolling_rank(high_minus_close, w_ma_high)
    MA_high = Ops.rolling_mean("High", w_ma_high)
    TermB = (pl.col("High") * R_HC) / (MA_high + eps)

    # 计算TermC
    VWAP_shifted = vwap.shift(5)
    VWAP_diff = vwap - VWAP_shifted
    R_VWAP_diff = Ops.rolling_rank(VWAP_diff, w_ma_high)
    TermC = R_VWAP_diff

    # 应用tanh变换
    TermA_tanh = TermA.tanh()
    TermB_tanh = TermB.tanh()
    TermC_tanh = TermC.tanh()

    # 组合最终因子
    factor_result = TermA_tanh * TermB_tanh - TermC_tanh

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_122")
