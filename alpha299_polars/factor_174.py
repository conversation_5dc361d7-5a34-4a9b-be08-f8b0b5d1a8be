# Alpha299因子 - factor_174 (Polars版本)
# 原始因子编号: 174
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_174(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha77因子：价格与VWAP偏离及价格与成交量相关性的衰减排名最小化因子（简化版）
    
    参数:
        w: 核心可调参数，默认为3
        uni_col: 单一基础数据列参数（本因子使用多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'linear_decay_20': 20,    # w，线性衰减窗口
        'zscore_20': 20,          # w，滚动标准化窗口
        'ma_40': 30,              # 2*w，移动平均窗口（限制为30）
        'corr_3': 3,              # 固定窗口，相关系数
        'linear_decay_6': 6       # 固定窗口，线性衰减
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    linear_decay_20 = window_sizes['linear_decay_20']
    zscore_20 = window_sizes['zscore_20']
    ma_40 = window_sizes['ma_40']
    corr_3 = window_sizes['corr_3']
    linear_decay_6 = window_sizes['linear_decay_6']

    # 1. 计算VWAP
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 2. 计算Mid_t
    mid_t = (pl.col("High") + pl.col("Low")) / 2

    # 3. 计算偏离度
    deviation = mid_t - vwap

    # 4. 线性衰减加权
    DL_1 = Ops.decaylinear(deviation, linear_decay_20)

    # 5. 滚动标准化
    dl1_mean = Ops.rolling_mean(DL_1, zscore_20)
    dl1_std = Ops.rolling_std(DL_1, zscore_20)
    Z_DL_1 = (DL_1 - dl1_mean) / (dl1_std + eps)

    # 6. 简化实现：使用时序排名代替横截面排名
    R_1 = Ops.rolling_rank(Z_DL_1, zscore_20)

    # 7. 计算成交量移动平均
    MA_40_Volume = Ops.rolling_mean("Volume", ma_40)

    # 8. 计算滚动相关系数
    Corr_2 = Ops.rolling_corr(mid_t, MA_40_Volume, corr_3)

    # 9. 线性衰减加权
    DL_2 = Ops.decaylinear(Corr_2, linear_decay_6)

    # 10. 简化实现：使用时序排名代替横截面排名
    R_2 = Ops.rolling_rank(DL_2, linear_decay_6)

    # 11. 最终因子值：min(R_1, R_2)
    factor_result = pl.min_horizontal([R_1, R_2])

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_174")
