# Alpha299因子 - factor_73 (Polars版本)
# 原始因子编号: 73
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_73(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于线性衰减的价格排名因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为3天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 3

    # 计算VWAP（简化为Amount/Volume）
    vwap = pl.col("Amount") / (pl.col("Volume") + eps)

    # 计算VWAP的线性衰减
    decay_vwap = Ops.decaylinear(vwap, window)

    # 对衰减结果进行排名
    rank_decay = Ops.rolling_rank(decay_vwap, window)

    # 只在最后使用一次.over()
    return rank_decay.over("symbol").cast(pl.Float32).alias("factor_73")
