# Alpha299因子 - factor_392 (Polars版本 - 准确实现)
# 原始因子编号: 392
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_392(w: int | None = 2, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及协方差、回归贝塔、移动平均、最小值、Z-score、截面排名、差分、标准差、除法、滚动最小值、乘积

    参数:
        w: 基础窗口大小，默认2
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    vwap_window = 20        # VWAP计算窗口
    cov_window = 11         # ts_cov窗口
    regbeta_window = 15     # ts_regbeta窗口
    mean_window = 4         # ts_mean窗口
    zscore_window = 11      # ts_zscore窗口
    delta_window = 2        # delta差值窗口
    std_window = 8          # ts_std窗口
    min_window = 9          # ts_min窗口
    
    # 1. 计算ts_cov(cov_window, amount, vwap)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    T1 = Ops.rolling_cov(amount_protected, pl.col("Vwap"), cov_window).over("symbol").alias("_T1")

    # 2. 计算ts_regbeta(amount, low, regbeta_window)
    T2 = Ops.rolling_regbeta(amount_protected, "Low", regbeta_window).over("symbol").alias("_T2")

    # 3. 计算ts_mean(T2, mean_window)
    T3 = Ops.rolling_mean(pl.col("_T2"), mean_window).over("symbol").alias("_T3")

    # 4. 计算gp_min(T1, T3)
    T4 = pl.min_horizontal([pl.col("_T1"), pl.col("_T3")]).alias("_T4")

    # 5. 计算ts_zscore(T4, zscore_window)
    T4_mean = Ops.rolling_mean(pl.col("_T4"), zscore_window)
    T4_std = Ops.rolling_std(pl.col("_T4"), zscore_window)
    T4_standardized = ((pl.col("_T4") - T4_mean) / (T4_std + eps)).over("symbol").alias("_T4_standardized")

    # 6. 截面排名（按时间点对所有symbol的T4_standardized进行排名）
    X1 = (pl.col("_T4_standardized").rank(method="average") / pl.len()).over("datetime").alias("_X1")
    
    # 7. 计算delta(amount, delta_window)
    T5 = (amount_protected - amount_protected.shift(delta_window)).over("symbol").alias("_T5")

    # 8. 计算ts_std(close, std_window)
    T6 = Ops.rolling_std("Close", std_window).over("symbol").alias("_T6")

    # 9. 计算div(T5, T6)
    T7 = (pl.col("_T5") / (pl.col("_T6") + eps)).alias("_T7")

    # 10. 计算ts_min(T7, min_window)
    X2 = Ops.rolling_min(pl.col("_T7"), min_window).over("symbol").alias("_X2")

    # 11. 计算最终因子值
    factor_result = (pl.col("_X1") * pl.col("_X2")).cast(pl.Float32).alias("factor_392")

    return [T1, T2, T3, T4, T4_standardized, X1, T5, T6, T7, X2, factor_result]
