# Alpha299因子 - factor_90 (Polars版本)
# 原始因子编号: 90
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_90(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 118因子：上下影线比率因子
    
    原始逻辑：
    1. 计算上影线长度：up_shadow = high - open
    2. 计算下影线长度：low_shadow = open - low
    3. 计算20期滚动求和
    4. 计算比率：(sum_up / sum_low) * 100
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期，默认为20天
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_90"
    """
    
    # 三段式混合模型窗口配置
    window_configs = {
        'rolling_window': 20,   # 滚动窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三段式混合模型：动态窗口计算函数
        """
        # 核心参数
        w_max = 300.0
        lambda_rate = 0.1
        alpha = 1.0
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口初始值相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三段式混合模型
        if w < min_base:
            # 模式A: 线性缩放
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 模式B: 锚点时刻，返回初始值
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else: # w > min_base
            # 模式C: 动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                final_value = min(max(final_value, w), w_max)
                results[k] = max(1, int(final_value))
            
            return results
    
    # 使用动态窗口计算
    window_sizes = calculate_window_sizes(w)
    rolling_window = window_sizes['rolling_window']
    
    # 计算每日上下影线长度
    up_shadow = pl.col("High") - pl.col("Open")
    low_shadow = pl.col("Open") - pl.col("Low")
    
    # 计算滚动求和
    sum_up = Ops.rolling_sum(up_shadow, rolling_window)
    sum_low = Ops.rolling_sum(low_shadow, rolling_window)
    
    # 计算比率并处理除以0的情况
    factor_value = (sum_up / (sum_low + eps)) * 100
    
    return factor_value.over("symbol").cast(pl.Float32).alias("factor_90")
