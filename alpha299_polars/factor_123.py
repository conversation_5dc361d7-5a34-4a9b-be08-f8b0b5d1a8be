# Alpha299因子 - factor_123 (Polars版本)
# 原始因子编号: 123
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_123(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha171因子：价格相对位置与动能比率因子
    根据公式：(Close_t - Low_t) * Open_t^5 / (High_t - Close_t) * Close_t^5
    
    参数:
        w: 不涉及时间窗口参数，设为None
        uni_col: 不涉及单一基础列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算分子和分母
    numerator = (pl.col("Close") - pl.col("Low")) * ((pl.col("Open") + eps) ** 5)
    denominator_part1 = (pl.col("High") - pl.col("Close"))
    denominator_part2 = (pl.col("Close") + eps) ** 5
    denominator = denominator_part1 * denominator_part2

    # 计算因子值
    factor_result = numerator / (denominator + eps)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_123")
