# Alpha299因子 - factor_274 (Polars版本 - 准确实现)
# 原始因子编号: 274
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_274(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP差分、截面排名、最大值、滚动最大值、相关系数、最小值、标准差

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    ts_max_window = w if w is not None else 5      # TS_MAX窗口
    ts_corr_window = w if w is not None else 5     # TS_CORR窗口
    std_window = w if w is not None else 5         # TS_STD窗口
    delta_window = w if w is not None else 5       # DELTA窗口

    # 1. DELTA(VWAP, delta_window)
    delta_vwap = (pl.col("Vwap") - pl.col("Vwap").shift(delta_window)).over("symbol").alias("_delta_vwap")

    # 2. RANK(AMOUNT) - 按时间分组排名
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    rank_amount = (amount_protected.rank(method="average") / pl.len()).over("datetime").alias("_rank_amount")

    # 3. GP_MAX(DELTA_VWAP, RANK_AMOUNT)
    gp_max = pl.max_horizontal([pl.col("_delta_vwap"), pl.col("_rank_amount")]).alias("_gp_max")

    # 4. TS_MAX(步骤3结果, ts_max_window)
    ts_max = Ops.rolling_max(pl.col("_gp_max"), ts_max_window).over("symbol").alias("_ts_max")

    # 5. TS_CORR(VWAP, AMOUNT, ts_corr_window)
    ts_corr = Ops.rolling_corr(pl.col("Vwap"), amount_protected, ts_corr_window).over("symbol").alias("_ts_corr")

    # 6. GP_MIN(步骤4结果, 步骤5结果)
    gp_min = pl.min_horizontal([pl.col("_ts_max"), pl.col("_ts_corr")]).alias("_gp_min")

    # 7. TS_STD(步骤6结果, std_window)
    factor_result = Ops.rolling_std(pl.col("_gp_min"), std_window).over("symbol").cast(pl.Float32).alias("factor_274")

    return [delta_vwap, rank_amount, gp_max, ts_max, ts_corr, gp_min, factor_result]
