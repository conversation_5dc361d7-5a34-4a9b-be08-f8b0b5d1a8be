# Alpha299因子 - factor_248 (Polars版本)
# 原始因子编号: 248
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_248(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha178因子：基于延迟16期的high与close的回归残差
    
    参数:
        w: 核心可调参数（本因子中无天数参数，故设为None）
        uni_col: 单一基础列参数（本因子使用high和close，故设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算延迟16期的high
    I1 = pl.col("High").shift(16)

    # 2. 计算I1与close的滚动协方差和I1的滚动方差
    cov = Ops.rolling_cov(I1, "Close", 14)
    var = Ops.rolling_var(I1, 14)

    # 3. 计算beta = Cov(I1, close) / Var(I1)
    beta = cov / (var + eps)

    # 4. 计算残差：close - beta * I1
    factor_result = pl.col("Close") - beta * I1

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_248")
