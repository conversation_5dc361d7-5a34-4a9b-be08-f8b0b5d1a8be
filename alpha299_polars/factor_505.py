# Alpha299因子 - factor_505 (Polars版本)
# 原始因子编号: 505
# 转写时间: 2025-07-18

import polars as pl

def factor_505(w: int | None = None, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算能量潮(On Balance Volume, OBV)指标
    
    参数:
        w: 窗口期参数，对于OBV指标不需要窗口期，设为None
        uni_col: 用于比较价格变化的列，默认使用'Close'收盘价
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算价格变化
    price_change = (pl.col(uni_col) - pl.col(uni_col).shift(1)).alias("_price_change")
    
    # 根据价格变化方向确定成交量的符号
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(0)
    signed_volume = pl.when(price_change > 0).then(volume_protected)\
                     .when(price_change < 0).then(-volume_protected)\
                     .otherwise(0).alias("_signed_volume")
    
    # 计算OBV：对signed_volume进行累计求和
    obv = signed_volume.cum_sum()
    
    # 只在最后使用一次.over()
    return obv.over("symbol").alias("factor_505")
