# Alpha299因子 - factor_147 (Polars版本 - 准确实现)
# 原始因子编号: 147
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_147(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及VWAP差分、滚动最大值、截面排名

    参数:
        w: 差分窗口，默认3
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    delta_window = w        # 差分窗口
    rolling_window = w + 2  # 滚动最大值窗口

    # 1. 计算差分 (Δ VWAP)
    Delta_VWAP = (pl.col("Vwap") - pl.col("Vwap").shift(delta_window)).over("symbol").alias("_Delta_VWAP")

    # 2. 计算滚动最大值 (MaxΔVWAP)
    Max_Delta_VWAP = Ops.rolling_max(pl.col("_Delta_VWAP"), rolling_window).over("symbol").alias("_Max_Delta_VWAP")

    # 3. 横截面百分比排名，取负
    cs_rank = ((pl.col("_Max_Delta_VWAP").rank(method="average") / pl.len()).over("datetime")).alias("_cs_rank")
    factor_result = (-pl.col("_cs_rank")).cast(pl.Float32).alias("factor_147")

    return [Delta_VWAP, Max_Delta_VWAP, cs_rank, factor_result]
