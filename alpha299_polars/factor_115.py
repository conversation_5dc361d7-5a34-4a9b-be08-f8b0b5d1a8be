# Alpha299因子 - factor_115 (Polars版本)
# 原始因子编号: 115
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_115(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha157因子：基于修正价格范围的加权指标
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'w1': 6,    # 第一个窗口期
        'w2': 12,   # 第二个窗口期 (2*w)
        'w3': 24    # 第三个窗口期 (4*w)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    w1 = window_sizes['w1']
    w2 = window_sizes['w2']
    w3 = window_sizes['w3']

    # 计算close_lag1
    close_lag1 = pl.col("Close").shift(1)

    # 计算修正最低价和最高价
    L_prime = pl.min_horizontal([pl.col("Low"), close_lag1])
    H_prime = pl.max_horizontal([pl.col("High"), close_lag1])
    Range_prime = H_prime - L_prime

    # 计算Term(w1)
    sum_L_w1 = Ops.rolling_sum(L_prime, w1)
    sum_Range_w1 = Ops.rolling_sum(Range_prime, w1)
    numerator_w1 = pl.col("Close") - sum_L_w1
    denominator_w1 = sum_Range_w1
    term_w1 = numerator_w1 / (denominator_w1 + eps)

    # 计算Term(w2)
    sum_L_w2 = Ops.rolling_sum(L_prime, w2)
    sum_Range_w2 = Ops.rolling_sum(Range_prime, w2)
    numerator_w2 = pl.col("Close") - sum_L_w2
    denominator_w2 = sum_Range_w2
    term_w2 = numerator_w2 / (denominator_w2 + eps)

    # 计算Term(w3)
    sum_L_w3 = Ops.rolling_sum(L_prime, w3)
    sum_Range_w3 = Ops.rolling_sum(Range_prime, w3)
    numerator_w3 = pl.col("Close") - sum_L_w3
    denominator_w3 = sum_Range_w3
    term_w3 = numerator_w3 / (denominator_w3 + eps)

    # 计算加权项
    X = term_w1 * w2 * w3
    Y = term_w2 * w1 * w3
    Z = term_w3 * w1 * w2

    # 最终因子计算
    total_weight = w1 * w2 + w1 * w3 + w2 * w3
    factor_result = (X + Y + Z) * (100 / (total_weight + eps))

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_115")
