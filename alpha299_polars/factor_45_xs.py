# Alpha299因子 - factor_45 (Polars版本 - 准确实现)
# 原始因子编号: 45
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_45(w: int | None = 10, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 105因子：基于开盘价和成交量的时序Z-score标准化、截面排名、滚动相关系数
    
    参数:
        w: 滚动窗口大小（单位：天），默认10
        uni_col: 本因子不依赖单一基础列，故设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 时序Z-score标准化
    # 开盘价的滚动Z-score
    open_mean = Ops.rolling_mean("Open", w).alias("_open_mean")
    open_std = Ops.rolling_std("Open", w).alias("_open_std")
    open_zscore = ((pl.col("Open") - open_mean) / (open_std + eps)).over("symbol").alias("_open_zscore")
    
    # 成交量的滚动Z-score
    volume_mean = Ops.rolling_mean("Volume", w).alias("_volume_mean")
    volume_std = Ops.rolling_std("Volume", w).alias("_volume_std")
    volume_zscore = ((pl.col("Volume") - volume_mean) / (volume_std + eps)).over("symbol").alias("_volume_zscore")
    
    # 2. 截面排名（按时间点）
    # 使用alias避免重复字段问题，然后进行截面排名
    open_rank = ((pl.col("_open_zscore").rank(method="average") / pl.len()).over("datetime")).alias("_open_rank")
    volume_rank = ((pl.col("_volume_zscore").rank(method="average") / pl.len()).over("datetime")).alias("_volume_rank")
    
    # 3. 滚动相关系数
    # 计算开盘价排名和成交量排名的滚动相关系数
    corr = - Ops.rolling_corr(pl.col("_open_rank"), pl.col("_volume_rank"), w).alias("_corr").over("symbol").cast(pl.Float32).alias("factor_45")
    

    # 只在最后使用一次.over()
    return [open_zscore, volume_zscore, open_rank, volume_rank, corr]
