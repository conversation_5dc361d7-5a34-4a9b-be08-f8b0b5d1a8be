# Alpha299因子 - factor_108 (Polars版本)
# 原始因子编号: 108
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_108(w: int | None = 20, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于高价与低价差值排名的因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为20天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义窗口
    window = w if w is not None else 20

    # 计算高价与低价的差值
    hl_diff = pl.col("High") - pl.col("Low")

    # 对差值进行排名
    rank_diff = Ops.rolling_rank(hl_diff, window)

    # 只在最后使用一次.over()
    return rank_diff.over("symbol").cast(pl.Float32).alias("factor_108")
