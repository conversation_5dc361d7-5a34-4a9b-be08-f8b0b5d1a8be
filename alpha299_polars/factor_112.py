# Alpha299因子 - factor_112 (Polars版本)
# 原始因子编号: 112
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_112(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha15隔夜跳空因子
    
    参数:
        w: 本因子不涉及天数参数，故默认为None
        uni_col: 本因子不依赖单一基础数据列，故默认为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算前一日收盘价
    close_prev = pl.col("Close").shift(1)

    # 计算隔夜跳空因子：(开盘价 / 前一日收盘价) - 1
    factor_result = (pl.col("Open") / (close_prev + eps)) - 1

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_112")
