#!/usr/bin/env python3
"""
alpha25因子测试工具
支持命令行参数测试指定因子

使用方法:
    python test_factors.py --i=18                    # 测试factor_18
    python test_factors.py --i="[19,21,22,23]"       # 测试多个因子
    python test_factors.py --factor=1                # 测试factor_1
    python test_factors.py --list                    # 列出所有可用因子
    python test_factors.py --all                     # 测试所有因子
    python test_factors.py --i=18 --sample=100000    # 使用10万行样本数据测试
    python test_factors.py --i=18 --w=20             # 指定窗口参数
"""

import sys
import os
import argparse
import importlib
from pathlib import Path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import polars as pl
import time
from ops.operators.config import load_data


def parse_factor_input(factor_input):
    """解析因子输入，支持单个数字或列表格式"""
    import ast

    if factor_input is None:
        return []

    # 尝试解析为列表
    if factor_input.startswith('[') and factor_input.endswith(']'):
        try:
            factor_list = ast.literal_eval(factor_input)
            if isinstance(factor_list, list):
                return [int(x) for x in factor_list]
        except (ValueError, SyntaxError):
            pass

    # 尝试解析为单个数字
    try:
        return [int(factor_input)]
    except ValueError:
        raise ValueError(f"无效的因子输入格式: {factor_input}")


def get_available_factors():
    """获取所有可用的因子列表"""
    alpha_dir = Path(__file__).parent
    factor_files = list(alpha_dir.glob("factor_*.py"))
    factors = []

    for file_path in factor_files:
        factor_name = file_path.stem  # 去掉.py后缀
        if factor_name.startswith("factor_") and factor_name[7:].isdigit():
            factor_num = int(factor_name[7:])
            factors.append((factor_num, factor_name))

    return sorted(factors, key=lambda x: x[0])


def load_factor(factor_num):
    """动态加载指定编号的因子函数"""
    try:
        factor_name = f"factor_{factor_num}"
        module_name = f"alpha25_polars.{factor_name}"

        # 动态导入模块
        module = importlib.import_module(module_name)
        factor_func = getattr(module, factor_name)

        return factor_func, factor_name
    except (ImportError, AttributeError) as e:
        print(f"❌ 无法加载因子 factor_{factor_num}: {e}")
        return None, None


def test_factor(factor_func, factor_name, data_df, **kwargs):
    """测试单个因子"""
    print(f"\n=== 测试 {factor_name} ===")

    try:
        start_time = time.time()

        # 获取因子表达式并计算结果
        factor_expr = factor_func(**kwargs)

        # 处理返回列表的情况
        if isinstance(factor_expr, list):            # 使用 with_columns 添加所有中间步骤
            for expr in factor_expr:
                data_df = data_df.with_columns(expr)
            result = data_df.select([
                pl.col("date"),
                pl.col("symbol"),
                pl.col("hhmm"),
                pl.col("datetime"),
                pl.col(factor_expr[-1].meta.output_name())  # 最后一个是因子值
            ])
        else:
            # 原来的单表达式处理方式
            result = data_df.select([
                pl.col("date"),
                pl.col("symbol"),
                pl.col("hhmm"),
                pl.col("datetime"),
                factor_expr
            ])
        end_time = time.time()

        print(f"✓ {factor_name} 计算成功")
        print(f"  - 计算时间: {end_time - start_time:.2f}秒")
        print(f"  - 结果形状: {result.shape}")
        print(f"  - 结果列: {result.columns}")

        # 显示结果统计信息
        if result.shape[0] > 0:
            factor_col = result.columns[-1]  # 因子列是最后一列
            # 过滤掉null和nan值后计算统计信息
            valid_values = pl.col(factor_col).filter(~pl.col(factor_col).is_null() & ~pl.col(factor_col).is_nan())
            factor_stats = result.select([
                pl.col(factor_col).count().alias("count"),
                valid_values.mean().alias("mean"),
                valid_values.std().alias("std"),
                valid_values.min().alias("min"),
                valid_values.max().alias("max"),
                pl.col(factor_col).null_count().alias("null_count"),
                (pl.col(factor_col).is_nan().sum()).alias("nan_count")
            ]).to_pandas().iloc[0]

            print(f"  - 因子统计:")
            print(f"    * 总行数: {result.shape[0]}")
            print(f"    * 有效值数量: {factor_stats['count']}")
            print(f"    * 空值数量: {factor_stats['null_count']}")
            print(f"    * NaN值数量: {factor_stats['nan_count']}")
            print(f"    * 均值: {factor_stats['mean']:.6f}")
            print(f"    * 标准差: {factor_stats['std']:.6f}")
            print(f"    * 最小值: {factor_stats['min']:.6f}")
            print(f"    * 最大值: {factor_stats['max']:.6f}")

            # 显示前几行结果
            print(f"  - 前5行结果:")
            print(result.head(5).to_pandas().to_string(index=False))
        else:
            print("  - 警告: 结果为空")

        return result

    except Exception as e:
        print(f"✗ {factor_name} 计算失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="alpha25因子测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python test_factors.py --i=18                    # 测试factor_18
  python test_factors.py --i="[19,21,22,23]"       # 测试多个因子
  python test_factors.py --factor=1                # 测试factor_1
  python test_factors.py --list                    # 列出所有可用因子
  python test_factors.py --all                     # 测试所有因子
  python test_factors.py --i=18 --sample=100000    # 使用10万行样本数据测试
  python test_factors.py --i=18 --w=20             # 指定窗口参数w
  python test_factors.py --i=18 --uni_col=Close    # 指定单列参数
        """
    )

    # 因子选择参数（互斥）
    factor_group = parser.add_mutually_exclusive_group(required=True)
    factor_group.add_argument("--i", "--factor", dest="factor_input",
                             help="测试指定编号的因子 (例如: --i=18 或 --i='[19,21,22,23]')")
    factor_group.add_argument("--list", action="store_true",
                             help="列出所有可用的因子")
    factor_group.add_argument("--all", action="store_true",
                             help="测试所有可用的因子")

    # 数据和参数选项
    parser.add_argument("--sample", type=int, default=10000000,
                       help="使用的样本数据行数 (默认: 10000000)")
    parser.add_argument("--w", type=int, default=None,
                       help="因子窗口参数w (默认: 使用因子默认值)")
    parser.add_argument("--uni_col", type=str, default=None,
                       help="单列参数uni_col (默认: None)")
    parser.add_argument("--eps", type=float, default=1e-8,
                       help="防除零参数eps (默认: 1e-8)")

    return parser.parse_args()


def list_available_factors():
    """列出所有可用的因子"""
    print("=== 可用的alpha25因子 ===")
    factors = get_available_factors()

    if not factors:
        print("❌ 未找到任何因子文件")
        return

    print(f"找到 {len(factors)} 个因子:")
    for factor_num, factor_name in factors:
        factor_file = Path(__file__).parent / f"{factor_name}.py"
        if factor_file.exists():
            print(f"  ✓ factor_{factor_num:3d} - {factor_file}")
        else:
            print(f"  ✗ factor_{factor_num:3d} - 文件不存在")


def test_single_factor(factor_num, data_df, **kwargs):
    """测试单个因子"""
    print(f"=== 测试 factor_{factor_num} ===")

    # 动态加载因子
    factor_func, factor_name = load_factor(factor_num)
    if factor_func is None:
        return None

    # 测试因子
    result = test_factor(factor_func, factor_name, data_df, **kwargs)
    return result


def test_all_factors(data_df, **kwargs):
    """测试所有可用因子"""
    print("=== 测试所有可用因子 ===")

    factors = get_available_factors()
    if not factors:
        print("❌ 未找到任何因子文件")
        return {}

    results = {}
    success_count = 0

    for factor_num, factor_name in factors:
        print(f"\n--- 测试 factor_{factor_num} ---")

        # 动态加载因子
        factor_func, _ = load_factor(factor_num)
        if factor_func is None:
            continue

        # 测试因子
        result = test_factor(factor_func, factor_name, data_df, **kwargs)
        if result is not None:
            results[factor_name] = result
            success_count += 1

    print(f"\n=== 批量测试总结 ===")
    print(f"成功测试: {success_count}/{len(factors)} 个因子")

    return results


def main():
    """主函数"""
    args = parse_arguments()

    # 处理列表命令
    if args.list:
        list_available_factors()
        return

    # 加载数据
    print("=== 加载数据 ===")
    try:
        data_df = load_data()
        print(f"✓ 数据加载成功")
        print(f"  - 数据形状: {data_df.shape}")
        print(f"  - 数据列: {data_df.columns}")

        # 使用样本数据
        if args.sample and args.sample < data_df.shape[0]:
            data_df = data_df.head(args.sample)
            print(f"  - 使用样本数据: {data_df.shape[0]}行")

    except Exception as e:
        print(f"❌ 数据加载失败: {str(e)}")
        return

    # 准备因子参数
    factor_kwargs = {}
    if args.w is not None:
        factor_kwargs['w'] = args.w
    if args.uni_col is not None:
        factor_kwargs['uni_col'] = args.uni_col
    if args.eps != 1e-8:
        factor_kwargs['eps'] = args.eps

    # 执行测试
    if args.factor_input:
        # 解析因子输入
        try:
            factor_nums = parse_factor_input(args.factor_input)
        except ValueError as e:
            print(f"❌ {str(e)}")
            return

        if len(factor_nums) == 1:
            # 测试单个因子
            result = test_single_factor(factor_nums[0], data_df, **factor_kwargs)
            if result is not None:
                print(f"\n✓ factor_{factor_nums[0]} 测试完成")
        else:
            # 测试多个因子
            print(f"\n=== 批量测试 {len(factor_nums)} 个因子 ===")
            success_factors = []
            failed_factors = []

            for factor_num in factor_nums:
                print(f"\n--- 测试 factor_{factor_num} ---")
                result = test_single_factor(factor_num, data_df, **factor_kwargs)
                if result is not None:
                    success_factors.append(factor_num)
                    print(f"✓ factor_{factor_num} 测试完成")
                else:
                    failed_factors.append(factor_num)

            print(f"\n=== 批量测试总结 ===")
            print(f"总测试因子数: {len(factor_nums)}")
            print(f"成功: {len(success_factors)} 个")
            print(f"失败: {len(failed_factors)} 个")

            if success_factors:
                print(f"✓ 成功的因子: {success_factors}")
            if failed_factors:
                print(f"✗ 失败的因子: {failed_factors}")

    elif args.all:
        # 测试所有因子
        results = test_all_factors(data_df, **factor_kwargs)
        print(f"\n✓ 批量测试完成，成功测试 {len(results)} 个因子")

if __name__ == "__main__":
    main()
