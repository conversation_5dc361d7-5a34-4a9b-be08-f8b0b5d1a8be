# Alpha299因子 - factor_54 (Polars版本)
# 原始因子编号: 54
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_54(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于高价排名与成交量Z-score排名相关性的负值因子（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'ma_window': 15,          # 移动平均窗口
        'zscore_window': 20,     # Z-score标准化窗口  
        'corr_window': 9         # 相关系数计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    ma_window = window_sizes['ma_window']
    zscore_window = window_sizes['zscore_window']
    corr_window = window_sizes['corr_window']

    # 简化实现：使用时序排名代替横截面排名
    # 步骤1: 计算RH_t (high的时序排名)
    RH_t = Ops.rolling_rank("High", corr_window)

    # 步骤2: 计算MA15(Volume)
    volume_protected = pl.col("Volume").clip(lower_bound=0)
    MAV_15 = Ops.rolling_mean(volume_protected, ma_window)

    # 步骤3: 计算Z-score标准化
    mav_mean = Ops.rolling_mean(MAV_15, zscore_window)
    mav_std = Ops.rolling_std(MAV_15, zscore_window)
    ZMAV_15 = (MAV_15 - mav_mean) / (mav_std + eps)

    # 步骤4: 计算RMAV_15 (Z-score后的时序排名)
    RMAV_15 = Ops.rolling_rank(ZMAV_15, corr_window)

    # 步骤5: 计算Corr_t (RH_t与RMAV_15的滚动相关系数)
    Corr_t = Ops.rolling_corr(RH_t, RMAV_15, corr_window)

    # 步骤6: 对Corr_t进行时序排名，取负
    rank_Corr = Ops.rolling_rank(Corr_t, corr_window)
    factor_result = -rank_Corr

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_54")
