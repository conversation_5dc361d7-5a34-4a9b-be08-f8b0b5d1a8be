# Alpha299因子 - factor_58 (Polars版本 - 准确实现)
# 原始因子编号: 58
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_58(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha 156因子：双重衰减排名最大值因子 III

    参数:
        w: 可调参数（本因子中无需调整，设为None）
        uni_col: 单列参数（本因子使用多列，设为None）
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 1. 计算VWAP的5期差分
    delta_vwap = (pl.col("Vwap") - pl.col("Vwap").shift(5)).alias("_delta_vwap")

    # 2. 对Delta VWAP进行3期线性衰减
    dl1 = Ops.decaylinear(pl.col("_delta_vwap"), 3).over("symbol").alias("_dl1")

    # 3. 横截面百分比排序R1
    r1 = ((pl.col("_dl1").rank(method="average") / pl.len()).over("datetime")).alias("_r1")

    # 4. 计算混合价格P_mix
    p_mix = (0.15 * pl.col("Open") + 0.85 * pl.col("Low")).alias("_p_mix")

    # 5. 计算混合价格的2期变化率
    p_mix_shifted = pl.col("_p_mix").shift(2).alias("_p_mix_shifted")
    roc_pmix = ((pl.col("_p_mix") - pl.col("_p_mix_shifted")) / (pl.col("_p_mix") + eps)).over("symbol").alias("_roc_pmix")

    # 6. 对-ROC进行3期线性衰减
    negative_roc = (-pl.col("_roc_pmix")).alias("_negative_roc")
    dl2 = Ops.decaylinear(pl.col("_negative_roc"), 3).over("symbol").alias("_dl2")

    # 7. 横截面百分比排序R2
    r2 = ((pl.col("_dl2").rank(method="average") / pl.len()).over("datetime")).alias("_r2")

    # 8. 计算最终因子值
    max_rank = pl.max_horizontal([pl.col("_r1"), pl.col("_r2")]).alias("_max_rank")
    factor_result = (-pl.col("_max_rank")).over("symbol").cast(pl.Float32).alias("factor_58")

    return [delta_vwap, dl1, r1, p_mix, p_mix_shifted, roc_pmix, negative_roc, dl2, r2, max_rank, factor_result]
