# Alpha299因子 - factor_335 (Polars版本 - 准确实现)
# 原始因子编号: 335
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_335(w: int | None = 3, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及协方差、回归贝塔、移动平均、最小值、Z-score、截面排名、差分、标准差、除法、滚动最小值、乘积

    参数:
        w: 基础窗口大小，默认3
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = 9      # ts_cov窗口
    n2 = 10     # ts_regbeta窗口
    n3 = 4      # ts_mean窗口
    n4 = 12     # ts_zscore窗口
    n5 = 3      # delta窗口
    n6 = 6      # ts_std窗口
    n7 = 7      # ts_min窗口
    
    # 1. 计算T1 = ts_cov(n1, amount, vwap)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    T1 = Ops.rolling_cov(amount_protected, pl.col("Vwap"), n1).over("symbol").alias("_T1")

    # 2. 计算T2 = ts_regbeta(amount, open_price, n2)
    T2 = Ops.rolling_regbeta(amount_protected, "Open", n2).over("symbol").alias("_T2")

    # 3. 计算T3 = ts_mean(T2, n3)
    T3 = Ops.rolling_mean(pl.col("_T2"), n3).over("symbol").alias("_T3")

    # 4. T4 = gp_min(T1, T3)
    T4 = pl.min_horizontal([pl.col("_T1"), pl.col("_T3")]).alias("_T4")

    # 5. T4_zscored = ts_zscore(T4, n4)
    T4_mean = Ops.rolling_mean(pl.col("_T4"), n4)
    T4_std = Ops.rolling_std(pl.col("_T4"), n4)
    T4_zscored = ((pl.col("_T4") - T4_mean) / (T4_std + eps)).over("symbol").alias("_T4_zscored")

    # 6. X1 = rank(T4_zscored)
    X1 = (pl.col("_T4_zscored").rank(method="average") / pl.len()).over("datetime").alias("_X1")
    
    # 7. T5 = delta(amount, n5)
    T5 = (amount_protected - amount_protected.shift(n5)).over("symbol").alias("_T5")

    # 8. T6 = ts_std(close, n6)
    T6 = Ops.rolling_std("Close", n6).over("symbol").alias("_T6")

    # 9. T7 = T5 / T6
    T7 = (pl.col("_T5") / (pl.col("_T6") + eps)).alias("_T7")

    # 10. X2 = ts_min(T7, n7)
    X2 = Ops.rolling_min(pl.col("_T7"), n7).over("symbol").alias("_X2")

    # 11. Alpha125 = X1 * X2
    factor_result = (pl.col("_X1") * pl.col("_X2")).cast(pl.Float32).alias("factor_335")

    return [T1, T2, T3, T4, T4_zscored, X1, T5, T6, T7, X2, factor_result]
