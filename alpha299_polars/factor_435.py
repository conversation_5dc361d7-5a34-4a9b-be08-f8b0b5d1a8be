# Alpha299因子 - factor_435 (Polars版本)
# 原始因子编号: 435
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def atr_numba(high, low, close, window):
    """
    使用Numba优化的平均真实波幅(ATR)计算
    """
    n = len(high)
    if n < window:
        return np.full(n, np.nan)
    
    atr = np.full(n, np.nan)
    tr = np.zeros(n)
    
    # 计算真实波幅(TR)
    for i in range(n):
        if i == 0:
            # 第一个数据点，TR = high - low
            tr[i] = high[i] - low[i]
        else:
            # TR = max(high - low, |high - prev_close|, |low - prev_close|)
            range1 = high[i] - low[i]
            range2 = abs(high[i] - close[i-1])
            range3 = abs(low[i] - close[i-1])
            tr[i] = max(range1, range2, range3)
    
    # 计算ATR
    if window <= 1:
        # 如果周期为1，ATR等于TR
        atr[:] = tr[:]
    else:
        # 计算初始ATR值（前N个TR的简单移动平均）
        for i in range(window - 1, n):
            if i == window - 1:
                # 第一个ATR值是前window个TR的平均值
                atr[i] = np.mean(tr[i-window+1:i+1])
            else:
                # 使用威尔德平滑法：ATR_t = ((N-1) * ATR_{t-1} + TR_t) / N
                atr[i] = ((window - 1) * atr[i-1] + tr[i]) / window
    
    return atr

def factor_435(w: int | None = 14, uni_col: str | None = None) -> pl.Expr:
    """
    计算平均真实波幅(ATR)因子
    
    参数:
        w: ATR计算的周期长度，默认为14
        uni_col: 单一数据列，本因子不使用单一列，设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'atr_window': 14
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    atr_window = window_sizes['atr_window']
    
    def apply_atr(high_values, low_values, close_values):
        return atr_numba(high_values, low_values, close_values, atr_window)
    
    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low"), pl.col("Close")],
        function=lambda x: pl.Series(apply_atr(x[0].to_numpy(), x[1].to_numpy(), x[2].to_numpy()))
    ).over("symbol").alias("factor_435")
