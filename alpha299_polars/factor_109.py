# Alpha299因子 - factor_109 (Polars版本)
# 原始因子编号: 109
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_109(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 150因子：成交量加权典型价格因子
    
    原始逻辑：
    1. 计算典型价格 TP = (Close + High + Low) / 3
    2. 因子值 = TP * Volume
    
    参数:
        w: 基准参数，本因子不使用窗口，设为None
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_109"
    """
    
    # 计算典型价格，分母加一个微小值防止除以0
    tp = (pl.col("Close") + pl.col("High") + pl.col("Low")) / (3 + eps)
    
    # 计算因子值
    factor_value = tp * pl.col("Volume")
    
    # 确保volume为非负数，虽然通常volume不会为负，但作为保护措施
    factor_value = pl.when(pl.col("Volume") >= 0).then(factor_value).otherwise(pl.lit(None))
    
    return factor_value.cast(pl.Float32).alias("factor_109")
