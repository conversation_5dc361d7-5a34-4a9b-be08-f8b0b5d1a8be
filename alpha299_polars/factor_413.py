# Alpha299因子 - factor_413 (Polars版本)
# 原始因子编号: 413
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from numba import njit

@njit
def fastk_numba(high, low, close, window):
    """
    使用Numba优化的FastK计算
    """
    n = len(high)
    if n < window:
        return np.full(n, np.nan)
    
    fastk = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        # 计算过去window个周期内的最高价和最低价
        start_idx = i - window + 1
        highest_high = np.max(high[start_idx:i+1])
        lowest_low = np.min(low[start_idx:i+1])
        
        # 计算价格区间差
        price_range = highest_high - lowest_low
        
        # 计算FastK值
        if price_range > 1e-8:  # 避免除以零
            fastk[i] = (close[i] - lowest_low) / price_range * 100.0
        else:
            fastk[i] = 0.0
    
    return fastk

def factor_413(w: int | None = 5, uni_col: str | None = None) -> pl.Expr:
    """
    计算快速随机指标K值 (Fast Stochastic %K, FastK)
    
    参数:
        w: 计算FastK值的时间窗口参数，默认为5
        uni_col: 这个因子不依赖于单一列，所以设为None
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'fastk_window': 5
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    fastk_window = window_sizes['fastk_window']
    
    def apply_fastk(high_values, low_values, close_values):
        return fastk_numba(high_values, low_values, close_values, fastk_window)

    return pl.map_batches(
        exprs=[pl.col("High"), pl.col("Low"), pl.col("Close")],
        function=lambda x: pl.Series(apply_fastk(x[0].to_numpy(), x[1].to_numpy(), x[2].to_numpy()))
    ).over("symbol").alias("factor_413")
