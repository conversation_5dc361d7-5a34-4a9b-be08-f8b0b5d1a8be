# Alpha299因子 - factor_198 (Polars版本)
# 原始因子编号: 198
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_198(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha104因子：基于成交量-低价相关性和成交量最大值变化率的反正切因子
    
    参数:
        w: 核心时间窗口参数（本因子不使用）
        uni_col: 单一基础列参数（本因子不使用）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 1. 计算X1: ts_corr(volume, low, 16)
    X1 = Ops.rolling_corr("Volume", "Low", 16)

    # 2. 计算T1: ts_max(volume, 20)
    T1 = Ops.rolling_max("Volume", 20)

    # 3. 计算X2: ts_pctchg(T1, 3)
    T1_lag3 = T1.shift(3)
    X2 = (T1 - T1_lag3) / (T1_lag3 + eps)

    # 4. 计算X3 = X1 + X2
    X3 = X1 + X2

    # 5. 计算Alpha104: arctan(X3)
    factor_result = X3.arctan()

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_198")
