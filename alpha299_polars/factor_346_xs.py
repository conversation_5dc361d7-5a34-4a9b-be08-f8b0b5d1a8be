# Alpha299因子 - factor_346 (Polars版本 - 准确实现)
# 原始因子编号: 346
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_346(w: int | None = 9, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算Alpha15因子：基于vwap差值标准化、amount排名、协方差等的复合计算

    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为9天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = 15     # delta(vwap, 15)
    n2 = 15     # ts_zscore(Y1, 15)
    n3 = 9      # ts_max(X3, 9)
    n4 = 9      # ts_cov(9, vwap, amount) 和 ts_zscore(Y5, 9)
    n5 = 14     # ts_std(X6, 14)
    
    # 1. 计算vwap在过去n1个周期内的差值: Y1 = delta(vwap, n1)
    Y1 = (pl.col("Vwap") - pl.col("Vwap").shift(n1)).over("symbol").alias("_Y1")

    # 2. 对Y1进行过去n2个周期的滚动标准化: X1 = ts_zscore(Y1, n2)
    Y1_mean = Ops.rolling_mean(pl.col("_Y1"), n2)
    Y1_std = Ops.rolling_std(pl.col("_Y1"), n2)
    X1 = ((pl.col("_Y1") - Y1_mean) / (Y1_std + eps)).over("symbol").alias("_X1")

    # 3. 计算成交额(amount)的截面排名: X2 = rank(amount)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    X2 = (amount_protected.rank(method="average") / pl.len()).over("datetime").alias("_X2")

    # 4. 取X1和X2中逐元素的较大值: X3 = gp_max(X1, X2)
    X3 = pl.max_horizontal([pl.col("_X1"), pl.col("_X2")]).alias("_X3")

    # 5. 计算X3在过去n3个周期内的滚动最大值: X4 = ts_max(X3, n3)
    X4 = Ops.rolling_max(pl.col("_X3"), n3).over("symbol").alias("_X4")
    
    # 6. 计算vwap和amount在过去n4个周期内的滚动协方差: Y5 = ts_cov(n4, vwap, amount)
    Y5 = Ops.rolling_cov(pl.col("Vwap"), amount_protected, n4).over("symbol").alias("_Y5")

    # 7. 对Y5进行过去n4个周期的滚动标准化: X5 = ts_zscore(Y5, n4)
    Y5_mean = Ops.rolling_mean(pl.col("_Y5"), n4)
    Y5_std = Ops.rolling_std(pl.col("_Y5"), n4)
    X5 = ((pl.col("_Y5") - Y5_mean) / (Y5_std + eps)).over("symbol").alias("_X5")

    # 8. 取X4和X5中逐元素的较大值: X6 = gp_max(X4, X5)
    X6 = pl.max_horizontal([pl.col("_X4"), pl.col("_X5")]).alias("_X6")

    # 9. 计算X6在过去n5个周期内的滚动标准差得到Alpha15: Alpha15 = ts_std(X6, n5)
    factor_result = Ops.rolling_std(pl.col("_X6"), n5).over("symbol").cast(pl.Float32).alias("factor_346")

    return [Y1, X1, X2, X3, X4, Y5, X5, X6, factor_result]
