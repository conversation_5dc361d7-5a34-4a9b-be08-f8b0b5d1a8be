# Alpha299因子 - factor_447 (Polars版本)
# 原始因子编号: 447
# 转写时间: 2025-07-18

import polars as pl

def factor_447(w: int | None = None, uni_col: str | None = 'Close') -> pl.Expr:
    """
    向量三角余弦因子 (Vector Trigonometric Cosine, COS) 计算函数
    
    参数:
        w: 窗口参数，本因子不需要窗口，因此为None
        uni_col: 用于计算的数据列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 对输入列的每个值应用余弦函数
    # polars的cos函数会自动处理inf和nan，结果为nan
    return pl.col(uni_col).cos().alias("factor_447")
