# Alpha299因子 - factor_215 (Polars版本)
# 原始因子编号: 215
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_215(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha132因子：基于low-volume最大值排名、volume Z-score排名与high-volume乘积比值和vwap-close和的协方差（简化版）
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为6天。
        uni_col: 单一基础数据列（本因子不适用，保留为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'rank_window': 6,      # ts_rank窗口
        'cov_window': 9,       # ts_cov窗口
        'zscore_window': 20    # ts_zscore窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['rank_window']
    n2 = window_sizes['cov_window']
    n3 = window_sizes['zscore_window']

    # 步骤1: T1 = max(low, volume)
    T1 = pl.max_horizontal([pl.col("Low"), pl.col("Volume")])

    # 步骤2: X1 = ts_rank(T1, n1)
    X1 = Ops.rolling_rank(T1, n1)

    # 步骤3: T2 = ts_zscore(volume, n3)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    volume_mean = Ops.rolling_mean(volume_protected, n3)
    volume_std = Ops.rolling_std(volume_protected, n3)
    T2 = (volume_protected - volume_mean) / (volume_std + eps)

    # 步骤4: T3 = rank(T2) - 简化实现：使用时序排名代替横截面排名
    T3 = Ops.rolling_rank(T2, n3)

    # 步骤5: T4 = mul(high, volume)
    T4 = pl.col("High") * pl.col("Volume")

    # 步骤6: T5 = div(T3, T4)
    T5 = T3 / (T4 + eps)

    # 步骤7: T6 = add(vwap, close)
    T6 = pl.col("Vwap") + pl.col("Close")

    # 步骤8: X2 = max(T5, T6)
    X2 = pl.max_horizontal([T5, T6])

    # 步骤9: ts_cov(X1, X2, n2)
    factor_result = Ops.rolling_cov(X1, X2, n2)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_215")
