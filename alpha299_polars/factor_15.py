# Alpha299因子 - factor_15 (Polars版本)
# 原始因子编号: 15
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_15(w: int | None = 2, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha因子：基于收益率标准差比值和价格差分的排名因子
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为2天。
        uni_col: 单一基础列参数（默认为'Close'）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'window_2': 2,          # 2期标准差和差分排名窗口
        'window_5': 5,          # 5期标准差窗口  
        'rank_window': 20       # 最终排名窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    window_2 = window_sizes['window_2']
    window_5 = window_sizes['window_5'] 
    rank_window = window_sizes['rank_window']

    # 1. 计算RETURNS（基于close的对数收益率）
    returns = (pl.col(uni_col) + eps).log() - (pl.col(uni_col).shift(1) + eps).log()

    # 2. 计算2期和5期标准差
    std_2 = Ops.rolling_std(returns, window_2)
    std_5 = Ops.rolling_std(returns, window_5)

    # 3. 计算标准差比值
    ratio_std = std_2 / (std_5 + eps)

    # 4. 对比值进行排名（正向排名）
    rank_std_ratio = Ops.rolling_rank(ratio_std, window_5)
    part1 = 1 - rank_std_ratio

    # 5. 计算CLOSE的1期差分
    delta_close = pl.col(uni_col) - pl.col(uni_col).shift(1)

    # 6. 对差分进行排名（正向排名）
    rank_delta_close = Ops.rolling_rank(delta_close, window_2)
    part2 = 1 - rank_delta_close

    # 7. 两部分相加并再次排名（正向排名）
    sum_parts = part1 + part2
    factor_result = Ops.rolling_rank(sum_parts, rank_window)
    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_15")
