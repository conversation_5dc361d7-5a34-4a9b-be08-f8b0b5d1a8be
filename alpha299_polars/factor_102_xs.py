# Alpha299因子 - factor_102 (Polars版本 - 准确实现)
# 原始因子编号: 102
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_102(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及多个Z-score、时序排名、线性衰减、相关系数等

    参数:
        w: 基础窗口大小，默认6
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n_decay1 = w        # 第一次线性衰减窗口
    n_decay2 = w        # 第二次线性衰减窗口
    n_rank1 = w         # 第一次时序排名窗口
    n_rank2 = w         # 第二次时序排名窗口
    n_corr = w          # 相关系数窗口
    n_rank3 = w         # 第三次时序排名窗口
    n_z = w             # Z-score窗口
    mav_window = w      # 移动平均窗口

    # Step 1: 对open, low, high, close进行ts_zscore
    open_mean = Ops.rolling_mean("Open", n_z)
    open_std = Ops.rolling_std("Open", n_z)
    Z_open = ((pl.col("Open") - open_mean) / (open_std + eps)).over("symbol").alias("_Z_open")

    low_mean = Ops.rolling_mean("Low", n_z)
    low_std = Ops.rolling_std("Low", n_z)
    Z_low = ((pl.col("Low") - low_mean) / (low_std + eps)).over("symbol").alias("_Z_low")

    high_mean = Ops.rolling_mean("High", n_z)
    high_std = Ops.rolling_std("High", n_z)
    Z_high = ((pl.col("High") - high_mean) / (high_std + eps)).over("symbol").alias("_Z_high")

    close_mean = Ops.rolling_mean("Close", n_z)
    close_std = Ops.rolling_std("Close", n_z)
    Z_close = ((pl.col("Close") - close_mean) / (close_std + eps)).over("symbol").alias("_Z_close")

    # Step 2: 计算排名组合项Term_1
    TSR_open = Ops.rolling_rank(pl.col("_Z_open"), n_rank1).over("symbol").alias("_TSR_open")
    TSR_low = Ops.rolling_rank(pl.col("_Z_low"), n_rank1).over("symbol").alias("_TSR_low")
    TSR_high = Ops.rolling_rank(pl.col("_Z_high"), n_rank1).over("symbol").alias("_TSR_high")
    TSR_close_z = Ops.rolling_rank(pl.col("_Z_close"), n_rank1).over("symbol").alias("_TSR_close_z")

    Term1 = (pl.col("_TSR_open") + pl.col("_TSR_low") - pl.col("_TSR_high") - pl.col("_TSR_close_z")).alias("_Term1")

    # Step 3: 应用线性衰减加权
    DL1 = Ops.decaylinear(pl.col("_Term1"), n_decay1).over("symbol").alias("_DL1")

    # Step 4: 对DL1进行横截面排名R1
    R1 = ((pl.col("_DL1").rank(method="average") / pl.len()).over("datetime")).alias("_R1")

    # Step 5: 计算收盘价的ts_rank
    TSR_close = Ops.rolling_rank("Close", n_rank1).over("symbol").alias("_TSR_close")

    # Step 6: 计算成交量均值的ts_rank
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    MAV_volume = Ops.rolling_mean(volume_protected, mav_window)
    TSR_MAV = Ops.rolling_rank(MAV_volume, n_rank2).over("symbol").alias("_TSR_MAV")

    # Step 7: 计算相关系数Corr_2
    Corr2 = Ops.rolling_corr(pl.col("_TSR_close"), pl.col("_TSR_MAV"), n_corr).over("symbol").alias("_Corr2")

    # Step 8: 应用线性衰减加权
    DL2 = Ops.decaylinear(pl.col("_Corr2"), n_decay2).over("symbol").alias("_DL2")

    # Step 9: 计算DL2的ts_rank
    R2 = Ops.rolling_rank(pl.col("_DL2"), n_rank3).over("symbol").alias("_R2")

    # Step 10: 计算最终因子值（取最小值）
    factor_result = pl.min_horizontal([pl.col("_R1"), pl.col("_R2")]).over("symbol").cast(pl.Float32).alias("factor_102")

    return [Z_open, Z_low, Z_high, Z_close, TSR_open, TSR_low, TSR_high, TSR_close_z,
            Term1, DL1, R1, TSR_close, TSR_MAV, Corr2, DL2, R2, factor_result]
