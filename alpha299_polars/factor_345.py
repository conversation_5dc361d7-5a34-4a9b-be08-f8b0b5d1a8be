# Alpha299因子 - factor_345 (Polars版本)
# 原始因子编号: 345
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_345(w: int | None = 7, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha159因子：基于amount最大值反正切与close-amount回归贝塔双重排名对low的回归残差
    
    参数:
        w: 基准参数，单位为天，代表因子内部最小窗口期。默认为7天。
        uni_col: 单一基础列参数（本因子不适用，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'n1': 10,  # ts_max(amount, 10)
        'n2': 12,  # ts_regbeta(close, amount, 12)
        'n3': 10,  # ts_rank(..., 10)
        'n4': 8,   # ts_rank(..., 8)
        'n5': 7    # ts_regres(..., 7)
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']

    # 使用别名避免重复字段问题
    # 1. 计算成交额在过去n1个周期内的滚动最大值
    T1 = Ops.rolling_max("Amount", n1).alias("_T1")

    # 2. 计算T1的反正切值
    M1 = (T1 + eps).arctan().alias("_M1")

    # 3. 计算close对amount的回归贝塔系数
    T2 = Ops.rolling_regbeta("Close", "Amount", n2).alias("_T2")

    # 4. 计算T2在过去n3个周期内的滚动排名
    T3 = (Ops.rolling_rank(T2, n3)).alias("_T3")

    # 5. 计算T3在过去n4个周期内的滚动排名
    T4 = (Ops.rolling_rank(T3, n4)).alias("_T4")

    # 6. 计算M1与T4的和
    X1 = (M1 + T4).alias("_X1")

    # 7. 计算X1对low的回归残差
    factor_result = Ops.rolling_regres("Low", X1, n5)

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_345")
