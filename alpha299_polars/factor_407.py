# Alpha299因子 - factor_407 (Polars版本)
# 原始因子编号: 407
# 转写时间: 2025-07-18

import polars as pl
import numpy as np

def factor_407(w: int | None = None, uni_col: str | None = 'Close') -> pl.Expr:
    """
    计算正弦函数因子 (Sine Function, SIN)
    
    参数:
        w: 窗口参数，对于正弦函数因子不需要窗口参数，设为None
        uni_col: 用于计算的数据列，默认为'Close'
        
    返回:
        pl.Expr: 因子表达式
    """
    # 对输入列的每个值应用正弦函数
    # 正弦函数定义域为实数，无需特殊处理inf, nan，polars会自动处理nan为nan
    return pl.col(uni_col).sin().cast(pl.Float32).alias("factor_407")
