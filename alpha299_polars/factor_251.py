# Alpha299因子 - factor_251 (Polars版本)
# 原始因子编号: 251
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_251(w: int | None = 6, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha182因子：基于volume绝对值与close平方根的回归贝塔sigmoid变换，然后与low的回归残差的反正切
    
    参数:
        w: 核心窗口参数，默认6，用于推导其他窗口
        uni_col: 单一基础列参数（此处设为None因为因子涉及多列）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 定义所有窗口的基准值
    window_configs = {
        'beta_window': 6,      # beta计算窗口
        'resid_window': 9      # 残差计算窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        三阶段混合动态窗口变换系统
        """
        # 核心参数
        w_max = 120.0
        lambda_rate = 0.1  
        alpha = 1.0
        
        # 获取窗口边界
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        w = max(1.0, float(w1_input))
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w)
                final_value = w + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w), w_max)))
            
            return results
    
    # 计算所有窗口的最终大小
    window_sizes = calculate_window_sizes(w)
    beta_window = window_sizes['beta_window']
    resid_window = window_sizes['resid_window']

    # 步骤1: 计算abs(volume)
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    I1 = volume_protected.abs()

    # 步骤2: 计算sqrt(abs(close))
    I2 = (pl.col("Close").abs() + eps).sqrt()

    # 步骤3: 计算ts_regbeta(I1, I2, beta_window)
    I3 = Ops.rolling_regbeta(I1, I2, beta_window)

    # 步骤4: 应用sigmoid函数
    I4 = 1.0 / (1.0 + (-I3).exp())

    # 步骤5: 计算ts_regres(I4, low, resid_window)
    I5 = Ops.rolling_regres(I4, "Low", resid_window)

    # 步骤6: 应用arctan
    factor_result = I5.arctan()

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_251")
