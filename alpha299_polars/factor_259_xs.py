# Alpha299因子 - factor_259 (Polars版本 - 准确实现)
# 原始因子编号: 259
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_259(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算复杂因子：涉及成交额与高价的滚动协方差、Z-score标准化、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    cov_window = w if w is not None else 5      # 协方差窗口
    zscore_window = w if w is not None else 5   # Z-score窗口

    # 1. 计算TS_COV(cov_window, AMOUNT, HIGH)
    amount_protected = pl.when(pl.col("Amount") > 0).then(pl.col("Amount")).otherwise(eps)
    ts_cov = Ops.rolling_cov(amount_protected, "High", cov_window).over("symbol").alias("_ts_cov")

    # 2. 计算TS_ZSCORE(zscore_window, ts_cov)
    cov_mean = Ops.rolling_mean(pl.col("_ts_cov"), zscore_window)
    cov_std = Ops.rolling_std(pl.col("_ts_cov"), zscore_window)
    ts_zscore = ((pl.col("_ts_cov") - cov_mean) / (cov_std + eps)).over("symbol").alias("_ts_zscore")

    # 3. RANK: 截面排名，按时间分组
    factor_result = (pl.col("_ts_zscore").rank(method="average") / pl.len()).over("datetime").cast(pl.Float32).alias("factor_259")

    return [ts_cov, ts_zscore, factor_result]
