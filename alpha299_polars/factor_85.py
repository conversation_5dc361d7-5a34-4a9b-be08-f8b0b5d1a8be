# Alpha299因子 - factor_85 (Polars版本)
# 原始因子编号: 85
# 转写时间: 2025-07-29

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_85(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha 111因子：资金流指标EMA差值因子
    
    原始逻辑：
    1. 计算资金流乘数 (MFM) = ((close - low) - (high - close)) / (high - low)
    2. 计算资金流量 (MFV) = MFM * volume
    3. 计算短期EMA (span=3) 和长期EMA (span=10)
    4. 因子值 = EMA_long - EMA_short
    
    参数:
        w: 基准参数，本因子不使用窗口，设为None
        uni_col: 单一基础列参数，本因子涉及多列，设为None
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式，别名为"factor_85"
    """
    
    # 计算资金流乘数 (MFM)
    numerator = (pl.col("Close") - pl.col("Low")) - (pl.col("High") - pl.col("Close"))
    denominator = pl.col("High") - pl.col("Low") + eps
    mfm = numerator / denominator
    
    # 计算资金流量 (MFV)
    mfv = mfm * pl.col("Volume")
    
    # 计算短期EMA (span=3) 和长期EMA (span=10)
    # 使用ewm_mean，alpha = 2/(span+1)
    alpha_short = 2.0 / (3 + 1)  # span=3
    alpha_long = 2.0 / (10 + 1)  # span=10
    
    ema_short = mfv.ewm_mean(alpha=alpha_short, adjust=False).over("symbol")
    ema_long = mfv.ewm_mean(alpha=alpha_long, adjust=False).over("symbol")
    
    # 计算因子值：EMA_long - EMA_short
    factor_value = ema_long - ema_short
    
    return factor_value.cast(pl.Float32).alias("factor_85")
