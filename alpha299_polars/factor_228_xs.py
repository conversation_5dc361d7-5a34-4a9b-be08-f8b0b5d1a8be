# Alpha299因子 - factor_228 (Polars版本 - 准确实现)
# 原始因子编号: 228
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops
from typing import List

def factor_228(w: int | None = 5, uni_col: str | None = None, eps: float = 1e-8) -> List[pl.Expr]:
    """
    计算非常复杂的因子：涉及平方根、时序排名、最大值、协方差、Z-score、截面排名

    参数:
        w: 基础窗口大小，默认5
        uni_col: 本因子不依赖单一基础列，因此设为None
        eps: 防止除零的小常数

    返回:
        List[pl.Expr]: 因子表达式列表
    """
    # 定义窗口大小
    n1 = w if w is not None else 5  # ts_rank窗口和ts_zscore窗口
    n2 = w if w is not None else 5  # ts_cov窗口和ts_zscore窗口

    # 1. 计算成交量的绝对值平方根
    volume_protected = pl.when(pl.col("Volume") > 0).then(pl.col("Volume")).otherwise(eps)
    sqrt_volume = volume_protected.abs().sqrt().alias("_sqrt_volume")

    # 2. 计算收盘价的滚动排名（ts_rank）
    ts_rank_close = Ops.rolling_rank("Close", n1).over("symbol").alias("_ts_rank_close")

    # 3. 取ts_rank和vwap的较大值
    gp_max_value = pl.max_horizontal([pl.col("_ts_rank_close"), pl.col("Vwap")]).alias("_gp_max_value")

    # 4. 计算sqrt_volume和gp_max_value的滚动协方差
    ts_cov_value = Ops.rolling_cov(pl.col("_sqrt_volume"), pl.col("_gp_max_value"), n2).over("symbol").alias("_ts_cov_value")

    # 5. 对协方差结果进行滚动标准化
    cov_mean = Ops.rolling_mean(pl.col("_ts_cov_value"), n2)
    cov_std = Ops.rolling_std(pl.col("_ts_cov_value"), n2)
    ts_zscore_X1 = ((pl.col("_ts_cov_value") - cov_mean) / (cov_std + eps)).over("symbol").alias("_ts_zscore_X1")

    # 6. 对收盘价进行滚动标准化
    close_mean = Ops.rolling_mean("Close", n1)
    close_std = Ops.rolling_std("Close", n1)
    ts_zscore_close = ((pl.col("Close") - close_mean) / (close_std + eps)).over("symbol").alias("_ts_zscore_close")

    # 7. 对标准化后的收盘价进行截面排名
    rank_T4 = (pl.col("_ts_zscore_close").rank(method="average") / pl.len()).over("datetime").alias("_rank_T4")

    # 8. 计算最终因子值
    factor_result = (pl.col("_ts_zscore_X1") + pl.col("_rank_T4")).cast(pl.Float32).alias("factor_228")

    return [sqrt_volume, ts_rank_close, gp_max_value, ts_cov_value, ts_zscore_X1, ts_zscore_close, rank_T4, factor_result]
