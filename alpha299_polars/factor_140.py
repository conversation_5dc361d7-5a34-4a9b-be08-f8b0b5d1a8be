# Alpha299因子 - factor_140 (Polars版本)
# 原始因子编号: 140
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_140(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha2因子：日内价格振幅结构因子
    
    参数:
        w: 以天为单位的核心可调参数（本因子无需调整，设为None）
        uni_col: 单一基础数据列参数（本因子使用多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算日内价格结构项 S_t
    numerator = 2 * pl.col("Close") - pl.col("High") - pl.col("Low")
    denominator = pl.col("High") - pl.col("Low")
    S_t = numerator / (denominator + eps)

    # 计算一期差分 ΔS_t
    delta_S_t = S_t - S_t.shift(1)

    # 计算最终因子值（取负）
    factor_result = -delta_S_t

    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_140")
