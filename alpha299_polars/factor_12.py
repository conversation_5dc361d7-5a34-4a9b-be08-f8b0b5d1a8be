# Alpha299因子 - factor_12 (Polars版本)
# 原始因子编号: 12
# 转写时间: 2025-07-18

import polars as pl
import numpy as np
from ops.operators.polars_operators import PolarsOperators as Ops

def factor_12(w: int | None = 1, uni_col: str | None = 'Close', eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha29因子，基于复杂的时间序列嵌套运算
    
    参数:
        w: 核心可调参数（天数），用于联动其他时间窗口参数，默认值为1
        uni_col: 单一基础数据列，默认为'Close'
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 窗口配置
    window_configs = {
        'n1': 5.0,   # w，Δ(CLOSE-1,5) 对应的5期差分
        'n2': 2.0,   # int(2*w/5) = int(2*5/5) = 2，ts_min窗口
        'n3': 1.0,   # int(1*w/5) = int(1*5/5) = 1，sum窗口
        'n4': 6.0,   # int(6*w/5) = int(6*5/5) = 6，delay的6期
        'n5': 5.0    # w，ts_rank窗口
    }
    
    def calculate_window_sizes(w1_input):
        """
        基于输入w1计算各个窗口大小的嵌套函数 - 三段式混合模型
        """
        # 模型参数
        w_max = 120.0
        lambda_rate = 0.1
        alpha = 1.0
        
        # 应用下限
        w1 = max(1.0, float(w1_input))
        
        # 计算参数
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        # 输入预处理
        if w1_input is None:
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        
        # 特殊情况：所有窗口相同
        if max_base == min_base:
            base_val = min_base
            return {k: max(1, int(min(max(base_val, w1), w_max))) for k in window_configs}
        
        # 三阶段混合变换
        if w1 < min_base:
            # 阶段A：线性缩放模式
            max_window_current_val = w1 * (max_base / min_base)
            return {k: max(1, int(max_window_current_val * (base_value / max_base)))
                    for k, base_value in window_configs.items()}
        elif w1 == min_base:
            # 阶段B：锚点模式
            return {k: max(1, int(v)) for k, v in window_configs.items()}
        else:
            # 阶段C：动态范围归一化
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            
            results = {}
            for k, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                results[k] = max(1, int(min(max(final_value, w1), w_max)))
            
            return results
    
    # 计算窗口大小
    window_sizes = calculate_window_sizes(w)
    n1 = int(window_sizes['n1'])
    n2 = int(window_sizes['n2'])
    n3 = int(window_sizes['n3'])
    n4 = int(window_sizes['n4'])
    n5 = int(window_sizes['n5'])
    
    # 步骤1: 计算收益率 RETURNS = (CLOSE - CLOSE.shift(1)) / CLOSE.shift(1)
    returns = (pl.col(uni_col) - pl.col(uni_col).shift(1)) / (pl.col(uni_col).shift(1) + eps)
    
    # 步骤2: 计算Δ(CLOSE-1, n1) = CLOSE - CLOSE.shift(n1)
    delta_close = pl.col(uni_col) - pl.col(uni_col).shift(n1)
    
    # 步骤3: rank(Δ(CLOSE-1, n1), n5) - 使用滚动排名
    rank1 = Ops.rolling_rank(delta_close, n5)
    
    # 步骤4: rank(rank(...), n5) - 对排名再次排名
    rank2 = Ops.rolling_rank(rank1, n5)
    
    # 步骤5: ts_min(..., n2) - 滚动最小值
    ts_min = Ops.rolling_min(rank2, n2)
    
    # 步骤6: sum(..., n3) - 滚动求和
    sum_val = Ops.rolling_sum(ts_min, n3)
    
    # 步骤7: log(...) - 对数变换，保护log输入
    log_val = (sum_val + eps).log()
    
    # 步骤8: scale(..., n5) - 标准化处理
    log_mean = Ops.rolling_mean(log_val, n5)
    log_std = Ops.rolling_std(log_val, n5)
    scale_val = (log_val - log_mean) / (log_std + eps)
    
    # 步骤9: rank(scale(...), n5) - 对标准化结果排名
    rank3 = Ops.rolling_rank(scale_val, n5)
    
    # 步骤10: rank(rank(...), n5) - 再次排名
    rank4 = Ops.rolling_rank(rank3, n5)
    
    # 步骤11: product(..., 1) - 窗口为1，等价于原值
    product_val = rank4
    
    # 步骤12: min(..., 5) - 截断到最大值5
    min_val = product_val.clip(upper_bound=5.0)
    
    # 步骤13: ts_rank(delay(-RETURNS, n4), n5) - 延迟负收益率的时序排名
    delayed_neg_returns = (-returns).shift(n4)
    ts_rank = Ops.rolling_rank(delayed_neg_returns, n5)
    
    # 步骤14: 合并两部分结果
    factor_result = min_val + ts_rank
    
    # 只在最后使用一次.over()
    return factor_result.over("symbol").cast(pl.Float32).alias("factor_12")
